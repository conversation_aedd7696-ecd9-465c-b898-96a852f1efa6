import { type Command, CommandResult } from "../Command";
import { serviceManager } from "../../services/ServiceManager";
import { logger } from "../../../shared";
import { Service, ServiceState } from "../../services/Service";

/**
 * A sub-command for listing all registered services.
 * @implements {Command}
 */
class ListServicesCommand implements Command {
    readonly name = "list";
    readonly aliases = ["ls"];
    readonly description = "Lists all registered services and their status";

    /**
     * Executes the command to list all registered services and their status.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     * @param _args - Command arguments (not used)
     * @param context - The context in which the command is being executed.
     */
    execute(_args: string[]): CommandResult {
        const services = serviceManager.getAllServices();
        if (services.length === 0) {
            return CommandResult.okay("No services are currently registered");
        }

        logger.section("Registered Services");
        services.forEach((service: Service) => {
            const connectionInfo = service.connectionStats;
            const statusIndicator = this.getStatusIndicator(service.state);
            const autoConnect = service.serviceConfig.autoConnect ? " (auto)" : "";

            logger.keyValue(
                `${statusIndicator} ${service.metadata.name}`,
                `${service.metadata.platform} v${service.metadata.version} - ${service.state}${autoConnect}`,
                20,
            );

            if (service.state === ServiceState.CONNECTED && connectionInfo.lastConnected) {
                logger.keyValue(
                    "   Connected",
                    connectionInfo.lastConnected.toLocaleString(),
                    20
                );
            }

            if (service.state === ServiceState.ERROR && service.lastError) {
                logger.keyValue(
                    "   Error",
                    service.lastError.message,
                    20
                );
            }
        });
        logger.newLine();
        return CommandResult.okay();
    }

    private getStatusIndicator(state: ServiceState): string {
        switch (state) {
            case ServiceState.CONNECTED: return "🟢";
            case ServiceState.CONNECTING: return "🟡";
            case ServiceState.RECONNECTING: return "🟠";
            case ServiceState.DISCONNECTED: return "⚫";
            case ServiceState.ERROR: return "🔴";
            default: return "❓";
        }
    }
}

/**
 * A sub-command for displaying detailed information about a specific service.
 * @implements {Command}
 */
class ServiceInfoCommand implements Command {
    readonly name = "info";
    readonly description = "Shows detailed information about a specific service";
    readonly usage = "info <serviceName>";

    /**
     * Executes the command to show service details.
     * @param {string[]} args - The arguments for the command, expecting a service name.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     */
    execute(args: string[]): CommandResult {
        const serviceName = args[0];
        if (!serviceName) {
            return CommandResult.error("Service name is required. Usage: service info <serviceName>");
        }

        const service = serviceManager.getService(serviceName);
        if (!service) {
            return CommandResult.error(`Service '${serviceName}' not found`);
        }

        const connectionStats = service.connectionStats;

        logger.section(`Service Info: ${service.metadata.name}`);
        logger.keyValue("Name", service.metadata.name, 20);
        logger.keyValue("Version", service.metadata.version, 20);
        logger.keyValue("Description", service.metadata.description, 20);
        logger.keyValue("Platform", service.metadata.platform, 20);
        logger.keyValue("State", service.state, 20);
        logger.keyValue("Essential", service.metadata.essential ? "Yes" : "No", 20);
        logger.keyValue("Requires Auth", service.metadata.requiresAuth ? "Yes" : "No", 20);
        logger.keyValue("Auto Connect", service.serviceConfig.autoConnect ? "Yes" : "No", 20);
        logger.keyValue("Max Retries", String(service.serviceConfig.maxRetries || 0), 20);
        logger.keyValue("Retry Delay", `${service.serviceConfig.retryDelay || 0}ms`, 20);

        if (connectionStats.lastConnected) {
            logger.keyValue("Last Connected", connectionStats.lastConnected.toLocaleString(), 20);
        }

        if (connectionStats.lastAttempt) {
            logger.keyValue("Last Attempt", connectionStats.lastAttempt.toLocaleString(), 20);
        }

        logger.keyValue("Attempts", String(connectionStats.attempts), 20);
        logger.keyValue("Is Retrying", connectionStats.isRetrying ? "Yes" : "No", 20);

        if (service.lastError) {
            logger.keyValue("Last Error", service.lastError.message, 20);
        }

        logger.newLine();
        return CommandResult.okay();
    }
}

/**
 * A sub-command for connecting to a service.
 * @implements {Command}
 */
class ConnectServiceCommand implements Command {
    readonly name = "connect";
    readonly description = "Connects to a specific service";
    readonly usage = "connect <serviceName>";

    /**
     * Executes the command to connect to a service.
     * @param {string[]} args - The arguments for the command, expecting a service name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const serviceName = args[0];
        if (!serviceName) {
            return CommandResult.error("Service name is required. Usage: service connect <serviceName>");
        }

        try {
            const result = await serviceManager.connectService(serviceName);
            if (result.success) {
                return CommandResult.okay(`Service '${serviceName}' connected successfully`);
            } else {
                return CommandResult.error(`Failed to connect service '${serviceName}': ${result.message}`, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to connect service '${serviceName}': ${err.message}`, err);
        }
    }
}

/**
 * A sub-command for disconnecting from a service.
 * @implements {Command}
 */
class DisconnectServiceCommand implements Command {
    readonly name = "disconnect";
    readonly description = "Disconnects from a specific service";
    readonly usage = "disconnect <serviceName>";

    /**
     * Executes the command to disconnect from a service.
     * @param {string[]} args - The arguments for the command, expecting a service name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const serviceName = args[0];
        if (!serviceName) {
            return CommandResult.error("Service name is required. Usage: service disconnect <serviceName>");
        }

        try {
            const result = await serviceManager.disconnectService(serviceName);
            if (result.success) {
                return CommandResult.okay(`Service '${serviceName}' disconnected successfully`);
            } else {
                return CommandResult.error(`Failed to disconnect service '${serviceName}': ${result.message}`, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to disconnect service '${serviceName}': ${err.message}`, err);
        }
    }
}

/**
 * A sub-command for reconnecting to a service.
 * @implements {Command}
 */
class ReconnectServiceCommand implements Command {
    readonly name = "reconnect";
    readonly description = "Reconnects to a specific service";
    readonly usage = "reconnect <serviceName>";

    /**
     * Executes the command to reconnect to a service.
     * @param {string[]} args - The arguments for the command, expecting a service name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     * @param context - The context in which the command is being executed.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const serviceName = args[0];
        if (!serviceName) {
            return CommandResult.error("Service name is required. Usage: service reconnect <serviceName>");
        }

        try {
            const result = await serviceManager.reconnectService(serviceName);
            if (result.success) {
                return CommandResult.okay(`Service '${serviceName}' reconnected successfully`);
            } else {
                return CommandResult.error(`Failed to reconnect service '${serviceName}': ${result.message}`, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to reconnect service '${serviceName}': ${err.message}`, err);
        }
    }
}

/**
 * A sub-command for sending a message through a service.
 * @implements {Command}
 */
class SendMessageCommand implements Command {
    readonly name = "send";
    readonly description = "Sends a message through a specific service";
    readonly usage = "send <serviceName> <message>";

    /**
     * Executes the command to send a message through a service.
     * @param {string[]} args - The arguments for the command, expecting service name and message.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const serviceName = args[0];
        const message = args.slice(1).join(" ");

        if (!serviceName) {
            return CommandResult.error("Service name is required. Usage: service send <serviceName> <message>");
        }

        if (!message) {
            return CommandResult.error("Message is required. Usage: service send <serviceName> <message>");
        }

        try {
            const result = await serviceManager.sendMessage(serviceName, message);
            if (result.success) {
                if (result.data) {
                    // Prioritize showing the echo field if it exists (more user-friendly)
                    if (result.data.echo) {
                        logger.blank(result.data.echo);
                    } else {
                        // Fall back to full response for services without echo
                        logger.blank(JSON.stringify(result.data, null, 2));
                    }
                }
                return CommandResult.okay(`Message sent through service '${serviceName}' successfully`);
            } else {
                return CommandResult.error(`Failed to send message through service '${serviceName}': ${result.message}`, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to send message through service '${serviceName}': ${err.message}`, err);
        }
    }
}

/**
 * A sub-command for displaying service statistics.
 * @implements {Command}
 */
class ServiceStatsCommand implements Command {
    readonly name = "stats";
    readonly description = "Shows service system statistics";

    /**
     * Executes the command to show service statistics.
     * @param {string[]} args - The arguments for the command.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     */
    execute(args: string[]): CommandResult {
        const stats = serviceManager.getStats();

        logger.section("Service System Statistics");
        logger.keyValue("Total Services", String(stats.totalServices), 20);
        logger.keyValue("Connected", String(stats.connectedServices), 20);
        logger.keyValue("Connecting", String(stats.connectingServices), 20);
        logger.keyValue("Disconnected", String(stats.disconnectedServices), 20);
        logger.keyValue("Error State", String(stats.errorServices), 20);
        logger.keyValue("Auto-Connect", String(stats.autoConnectServices), 20);
        logger.keyValue("Essential", String(stats.essentialServices), 20);
        logger.newLine();

        return CommandResult.okay();
    }
}

/**
 * The main command for managing services. It serves as a parent for various sub-commands.
 * If called without a sub-command, it displays help information for its sub-commands.
 * @implements {Command}
 */
export default class ServiceCommand implements Command {
    readonly name = "service";
    readonly aliases = ["services"];
    readonly description = "Manages services";
    readonly subCommands: Command[] = [
        new ListServicesCommand(),
        new ServiceInfoCommand(),
        new ConnectServiceCommand(),
        new DisconnectServiceCommand(),
        new ReconnectServiceCommand(),
        new SendMessageCommand(),
        new ServiceStatsCommand(),
    ];

    /**
     * Executes the main service command.
     * Displays help information for the available sub-commands.
     * @param {string[]} args - The arguments for the command.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     */
    execute(args: string[]): CommandResult {
        logger.section("Service Command Help");
        logger.blank("Use 'service <sub-command>' to manage services.");
        logger.newLine();

        this.subCommands.forEach(cmd => {
            let usage = cmd.usage ? cmd.usage : cmd.name;
            if (cmd.aliases && cmd.aliases.length > 0) {
                usage += ` (${cmd.aliases.join(", ")})`;
            }
            const description = cmd.description ? `${cmd.description} (Usage: service ${usage})` : `(Usage: service ${usage})`;
            logger.keyValue(cmd.name, description, 15);
        });

        logger.newLine();
        return CommandResult.okay();
    }
}

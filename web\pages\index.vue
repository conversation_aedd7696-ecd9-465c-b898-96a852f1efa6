<template>
    <div class="page-container scrollable-content">
        <PageHeader title="Bot Overview" subtitle="Monitor your bot's performance and status" />

        <div class="stats-cards-grid" style="margin-bottom: var(--space-xl);">
            <StatCard title="App Memory" :value="stats.appMemory.value.value" :subtitle="stats.appMemory.subtitle.value" :progress-percentage="stats.appMemory.progress.value">
                <template #icon>
                    <Icon name="lucide:server" size="24" />
                </template>
            </StatCard>

            <StatCard title="System Memory" :value="stats.systemMemory.value.value" :subtitle="stats.systemMemory.subtitle.value" :progress-percentage="stats.systemMemory.progress.value">
                <template #icon>
                    <Icon name="lucide:cpu" size="24" />
                </template>
            </StatCard>

            <StatCard title="Loaded Plugins" :value="stats.plugins.value.value" :subtitle="stats.plugins.subtitle.value" :progress-percentage="stats.plugins.progress.value">
                <template #icon>
                    <Icon name="lucide:compass" size="24" />
                </template>
            </StatCard>

            <StatCard title="Available Commands" :value="stats.commands.value.value">
                <template #icon>
                    <Icon name="lucide:terminal" size="24" />
                </template>
            </StatCard>

            <StatCard title="Connected Services" :value="stats.connectedServices.value.value.value" :subtitle="stats.connectedServices.value.subtitle.value" :progress-percentage="stats.connectedServices.value.progress.value">
                <template #icon>
                    <Icon name="lucide:link" size="24" />
                </template>
            </StatCard>

            <StatCard title="Total Services" :value="stats.totalServices.value.value.value">
                <template #icon>
                    <Icon name="lucide:globe" size="24" />
                </template>
            </StatCard>

            <StatCard title="Connected Clients" :value="stats.clients.value.value">
                <template #icon>
                    <Icon name="lucide:users" size="24" />
                </template>
            </StatCard>

            <StatCard title="Message History" :value="stats.history.value.value":subtitle="stats.history.subtitle.value" :progress-percentage="stats.history.progress.value">
                <template #icon>
                    <Icon name="lucide:message-circle" size="24" />
                </template>
            </StatCard>
        </div>

        <div class="content-grid two-column-grid">
            <div class="activity-feed glass">
                <h2>Recent Activity</h2>
                <div class="activity-list">
                    <div v-for="activity in recentActivity" :key="activity.id" class="activity-item">
                        <div :class="['activity-icon', activity.type]">
                            <Icon :name="getActivityIcon(activity.type)" size="16" />
                        </div>
                        <div class="activity-content">
                            <p class="activity-message">{{ activity.message }}</p>
                            <p class="activity-time">{{ formatTime(activity.timestamp) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="quick-actions glass">
                <h2>Quick Actions</h2>
                <div class="actions-grid">
                    <button class="action-btn">
                        <Icon name="lucide:play" size="20" />
                        Start All Services
                    </button>
                    <button class="action-btn">
                        <Icon name="lucide:pause" size="20" />
                        Pause All Services
                    </button>
                    <button class="action-btn">
                        <Icon name="lucide:refresh-cw" size="20" />
                        Restart Bot
                    </button>
                    <button class="action-btn">
                        <Icon name="lucide:download" size="20" />
                        Export Logs
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useSystemStats } from '~/composables/useSystemStats'

// Use the simplified stats composable
const stats = useSystemStats()

// Mock activity data - could be replaced with real data later
const recentActivity = ref([
    { id: 1, type: 'connect', message: 'Discord service connected successfully', timestamp: Date.now() - 300000 },
    { id: 2, type: 'message', message: 'Processed 1,000 messages in Discord #general', timestamp: Date.now() - 600000 },
    { id: 3, type: 'error', message: 'Failed to connect to Twitch API - retrying...', timestamp: Date.now() - 900000 },
    { id: 5, type: 'info', message: 'Daily backup completed', timestamp: Date.now() - 1200000 }
])

const formatTime = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)

    if (minutes < 1) return 'just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return new Date(timestamp).toLocaleDateString()
}

const getActivityIcon = (type: string): string => {
    switch (type) {
        case 'connect':
            return 'lucide:radio'
        case 'message':
            return 'lucide:message-circle'
        case 'error':
            return 'lucide:circle-alert'
        case 'success':
            return 'lucide:check'
        default:
            return 'lucide:info'
    }
}
</script>

<style scoped>
/* Activity feed and quick actions specific styles */

.activity-feed,
.quick-actions {
    padding: var(--space-lg);
    border-radius: 12px;
}

.activity-feed h2,
.quick-actions h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon.connect {
    background: rgba(76, 175, 80, 0.2);
    color: var(--color-success);
}

.activity-icon.message {
    background: rgba(100, 181, 246, 0.2);
    color: var(--color-info);
}

.activity-icon.error {
    background: rgba(244, 67, 54, 0.2);
    color: var(--color-error);
}

.activity-icon.success {
    background: rgba(76, 175, 80, 0.2);
    color: var(--color-success);
}

.activity-icon.info {
    background: rgba(255, 193, 7, 0.2);
    color: var(--color-warning);
}

.activity-content {
    flex: 1;
}

.activity-message {
    font-size: 14px;
    margin-bottom: var(--space-xs);
}

.activity-time {
    font-size: 12px;
    color: var(--text-muted);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
}

/* Action buttons now use global styles */

@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

.progress-bar {
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    margin-top: var(--space-md);
    width: 100%;
}

.progress-fill {
    height: 100%;
    background-color: var(--color-success);
    transition: width 0.3s ease;
}
</style>

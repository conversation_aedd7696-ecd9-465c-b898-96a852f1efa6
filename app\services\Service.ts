import { logger } from "../../shared";
import { eventManager } from "../events/EventManager";
import type { Event, EventListener } from "../events/Event";

/**
 * Represents the current connection state of a service.
 */
export enum ServiceState {
    /** Service is disconnected from external platform */
    DISCONNECTED = "DISCONNECTED",
    /** Service is currently connecting */
    CONNECTING = "CONNECTING",
    /** Service is connected and active */
    CONNECTED = "CONNECTED",
    /** Service is attempting to reconnect after connection loss */
    RECONNECTING = "RECONNECTING",
    /** Service encountered an error */
    ERROR = "ERROR",
}

/**
 * Interface defining service metadata and configuration.
 */
export interface ServiceMetadata {
    /** Unique identifier for the service */
    readonly name: string;
    /** Semantic version of the service implementation */
    readonly version: string;
    /** Human-readable description of the service */
    readonly description: string;
    /** Platform or external service this connects to */
    readonly platform: string;
    /** Whether this service is essential and should auto-reconnect */
    readonly essential?: boolean;
    /** Whether this service requires authentication */
    readonly requiresAuth?: boolean;
}

/**
 * Configuration interface for service connections.
 */
export interface ServiceConfig {
    /** Whether the service should auto-connect on startup */
    autoConnect?: boolean;
    /** Connection timeout in milliseconds */
    connectionTimeout?: number;
    /** Reconnection retry attempts */
    maxRetries?: number;
    /** Delay between reconnection attempts in milliseconds */
    retryDelay?: number;
    /** Service-specific configuration options */
    options?: Record<string, any>;
}

/**
 * Result of a service lifecycle operation.
 */
export class ServiceResult {
    constructor(
        public readonly success: boolean,
        public readonly message?: string,
        public readonly error?: Error,
        public readonly data?: any
    ) {}

    static success(message?: string, data?: any): ServiceResult {
        return new ServiceResult(true, message, undefined, data);
    }

    static error(message?: string, error?: Error): ServiceResult {
        return new ServiceResult(false, message, error);
    }
}

/**
 * Base class that all services must extend.
 * Provides connection lifecycle management, state tracking, and event handling.
 */
export abstract class Service {
    /** Service metadata information */
    abstract readonly metadata: ServiceMetadata;

    /** Service configuration */
    protected config: ServiceConfig;

    /** Current state of the service */
    private _state: ServiceState = ServiceState.DISCONNECTED;

    /** Active event listeners registered by this service */
    private _activeListeners: Array<[Event<any>, EventListener<any>]> = [];

    /** Error that occurred during service operation, if any */
    private _lastError?: Error;

    /** Connection attempt counter for retry logic */
    private _connectionAttempts: number = 0;

    /** Reconnection timer reference */
    private _reconnectTimer?: NodeJS.Timeout;

    /** Last successful connection timestamp */
    private _lastConnected?: Date;

    /** Last connection attempt timestamp */
    private _lastAttempt?: Date;

    /**
     * Creates a new service instance.
     * @param config Service configuration options
     */
    constructor(config: ServiceConfig = {}) {
        this.config = {
            autoConnect: false,
            connectionTimeout: 30000, // 30 seconds
            maxRetries: 3,
            retryDelay: 5000, // 5 seconds
            ...config
        };
    }

    /**
     * Gets the current state of the service.
     */
    get state(): ServiceState {
        return this._state;
    }

    /**
     * Gets the service configuration.
     */
    get serviceConfig(): Readonly<ServiceConfig> {
        return { ...this.config };
    }

    /**
     * Gets the last error that occurred, if any.
     */
    get lastError(): Error | undefined {
        return this._lastError;
    }

    /**
     * Gets connection statistics.
     */
    get connectionStats() {
        return {
            state: this._state,
            attempts: this._connectionAttempts,
            lastConnected: this._lastConnected,
            lastAttempt: this._lastAttempt,
            isRetrying: !!this._reconnectTimer
        };
    }

    /**
     * Sets the service state (internal use only).
     */
    protected setState(state: ServiceState, error?: Error) {
        const previousState = this._state;
        this._state = state;
        this._lastError = error;

        if (state !== previousState) {
            logger.debug(this.metadata.name, `State changed: ${previousState} → ${state}`);
        }
    }

    /**
     * Registers an event listener that will be automatically removed when the service disconnects.
     * @param event The event to listen for.
     * @param listener The function to execute when the event is emitted.
     */
    protected registerListener<T>(event: Event<T>, listener: EventListener<T>) {
        eventManager.on(event, listener);
        this._activeListeners.push([event, listener]);
        logger.debug(this.metadata.name, `Registered listener for event: ${event.name}`);
    }

    /**
     * Unregisters all event listeners that were registered by this service.
     * This is called automatically when the service disconnects.
     */
    private unregisterAllListeners() {
        for (const [event, listener] of this._activeListeners) {
            eventManager.off(event, listener);
        }
        this._activeListeners = [];
        logger.debug(this.metadata.name, `Unregistered all event listeners`);
    }

    /**
     * Called when the service should establish connection to external platform.
     * Override this method to implement service-specific connection logic.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    abstract onConnect(): Promise<ServiceResult>;

    /**
     * Called when the service should disconnect from external platform.
     * Override this method to implement service-specific disconnection logic.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    abstract onDisconnect(): Promise<ServiceResult>;

    /**
     * Called when the service should send a message to the external platform.
     * Override this method to implement service-specific message sending.
     * @param message The message to send
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    abstract onSendMessage(message: any): Promise<ServiceResult>;

    /**
     * Called when the service should check its connection health.
     * Override this method to implement service-specific health checks.
     * @returns Promise resolving to a ServiceResult indicating health status.
     */
    async onHealthCheck(): Promise<ServiceResult> {
        // Default implementation - services can override
        return ServiceResult.success("Health check passed");
    }

    /**
     * Connects to the external service.
     */
    async connect(): Promise<ServiceResult> {
        if (this._state === ServiceState.CONNECTED) {
            return ServiceResult.error(`Service '${this.metadata.name}' is already connected`);
        }

        if (this._state === ServiceState.CONNECTING) {
            return ServiceResult.error(`Service '${this.metadata.name}' is already connecting`);
        }

        try {
            this.setState(ServiceState.CONNECTING);
            this._connectionAttempts++;
            this._lastAttempt = new Date();

            logger.debug(this.metadata.name, `Attempting to connect (attempt ${this._connectionAttempts})`);

            const result = await this.onConnect();

            if (result.success) {
                this.setState(ServiceState.CONNECTED);
                this._lastConnected = new Date();
                // Keep attempts count to show how many attempts it took to connect
            } else {
                const error = result.error || new Error(result.message || "Unknown connection error");
                this.setState(ServiceState.ERROR, error);
                logger.error(this.metadata.name, `Failed to connect: ${result.message}`, error);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.setState(ServiceState.ERROR, err);
            const message = `Service '${this.metadata.name}' connection failed`;
            logger.error(this.metadata.name, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Disconnects from the external service.
     */
    async disconnect(): Promise<ServiceResult> {
        if (this._state === ServiceState.DISCONNECTED) {
            return ServiceResult.error(`Service '${this.metadata.name}' is already disconnected`);
        }

        // Clear any pending reconnection timer
        this.clearReconnectTimer();

        try {
            logger.info(this.metadata.name, `Disconnecting`);

            const result = await this.onDisconnect();

            if (result.success) {
                this.unregisterAllListeners();
                this.setState(ServiceState.DISCONNECTED);
                this._connectionAttempts = 0; // Reset attempts for next connection cycle
            } else {
                const error = result.error || new Error(result.message || "Unknown disconnection error");
                this.setState(ServiceState.ERROR, error);
                logger.error(this.metadata.name, `Failed to disconnect cleanly: ${result.message}`, error);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.setState(ServiceState.ERROR, err);
            const message = `Service '${this.metadata.name}' disconnection failed`;
            logger.error(this.metadata.name, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Attempts to reconnect to the external service with retry logic.
     */
    async reconnect(): Promise<ServiceResult> {
        if (this._connectionAttempts >= (this.config.maxRetries || 3)) {
            let message = `Max reconnection attempts exceeded`;
            logger.error(this.metadata.name, message);
            message = `Service '${this.metadata.name}' ${message}`;
            this.setState(ServiceState.ERROR, new Error(message));
            return ServiceResult.error(message);
        }

        this.setState(ServiceState.RECONNECTING);

        // Wait for retry delay before attempting reconnection
        if (this.config.retryDelay && this.config.retryDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }

        return await this.connect();
    }

    /**
     * Schedules an automatic reconnection attempt.
     */
    private scheduleReconnect() {
        if (this._reconnectTimer) {
            return; // Already scheduled
        }

        const delay = this.config.retryDelay || 5000;
        logger.debug(this.metadata.name, `Scheduling reconnect in ${delay}ms`);

        this._reconnectTimer = setTimeout(() => {
            this._reconnectTimer = undefined;
            this.reconnect().catch(error => {
                logger.error(this.metadata.name, `Auto-reconnect failed`, error);
            });
        }, delay);
    }

    /**
     * Clears any pending reconnection timer.
     */
    private clearReconnectTimer() {
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = undefined;
        }
    }

    /**
     * Sends a message through this service.
     */
    async sendMessage(message: any): Promise<ServiceResult> {
        if (this._state !== ServiceState.CONNECTED) {
            return ServiceResult.error(`Service '${this.metadata.name}' is not connected`);
        }

        try {
            return await this.onSendMessage(message);
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            logger.error(this.metadata.name, `Failed to send message`, err);
            return ServiceResult.error("Failed to send message", err);
        }
    }

    /**
     * Performs a health check on the service connection.
     */
    async healthCheck(): Promise<ServiceResult> {
        try {
            return await this.onHealthCheck();
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            logger.error(this.metadata.name, `Health check failed`, err);
            return ServiceResult.error("Health check failed", err);
        }
    }

    /**
     * Handles connection loss and triggers reconnection if configured.
     */
    protected handleConnectionLoss(error?: Error) {
        logger.warn(this.metadata.name, `Connection lost`, error);
        this.setState(ServiceState.DISCONNECTED, error);

        // Schedule reconnection for essential services
        if (this.metadata.essential && this._connectionAttempts < (this.config.maxRetries || 3)) {
            this.scheduleReconnect();
        }
    }

    /**
     * Cleanup method called when service is being removed.
     */
    async cleanup(): Promise<void> {
        this.clearReconnectTimer();
        this.unregisterAllListeners();
        this._connectionAttempts = 0; // Reset attempts during cleanup

        if (this._state === ServiceState.CONNECTED || this._state === ServiceState.CONNECTING) {
            await this.disconnect().catch(error => {
                logger.error(this.metadata.name, `Cleanup disconnect failed`, error);
            });
        }
    }
}

name: Package Application

env:
    SHORT_SHA: "unknown"

on:
    push:
        branches:
            - main
    workflow_dispatch:

jobs:
    build-app:
        name: Build App
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
              with:
                  node-version: "22"
                  cache: "npm"

            - name: Install dependencies
              run: npm ci

            - name: Build app
              run: npm run app:build

            - name: Get Short Commit SHA
              run: echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

            - name: Upload app artifact
              uses: actions/upload-artifact@v4
              with:
                  name: chatbotts-app-${{ env.SHORT_SHA }}
                  path: app/dist/
                  include-hidden-files: true
                  if-no-files-found: error

    build-web:
        name: Build Web App
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v4
            - uses: actions/setup-node@v4
              with:
                  node-version: "22"
                  cache: "npm"

            - name: Install dependencies
              run: npm ci

            - name: Generate static site
              run: npm run web:build

            - name: Get Short Commit SHA
              run: echo "SHORT_SHA=$(git rev-parse --short HEAD)" >> $GITHUB_ENV

            - name: Upload web app artifact
              uses: actions/upload-artifact@v4
              with:
                  name: chatbotts-web-${{ env.SHORT_SHA }}
                  path: web/.output/public
                  include-hidden-files: true
                  if-no-files-found: error

import { logger } from "../../shared";
import type { Event, EventListener } from "./Event";

/**
 * Manages the registration of event listeners and the dispatching of events.
 * It allows different parts of the application to communicate in a decoupled manner.
 * This class follows a singleton pattern.
 */
export class EventManager {
    /**
     * Stores the listeners for each event.
     * The key is the event's unique symbol, and the value is a Set of listeners.
     * Using a Set ensures that the same listener is not registered multiple times.
     */
    private readonly listeners = new Map<symbol, Set<EventListener<any>>>();

    /**
     * Registers a listener for a specific event.
     *
     * @template T The type of the event's payload.
     * @param event The event to listen for.
     * @param listener The function to execute when the event is emitted.
     */
    on<T>(event: Event<T>, listener: EventListener<T>) {
        const eventSymbol = event._symbol;
        if (!this.listeners.has(eventSymbol)) {
            this.listeners.set(eventSymbol, new Set());
        }
        this.listeners.get(eventSymbol)!.add(listener);
        //logger.debug(`Listener registered for event: ${event.name}`);
    }

    /**
     * Unregisters a listener from a specific event.
     *
     * @template T The type of the event's payload.
     * @param event The event to stop listening for.
     * @param listener The function to remove.
     */
    off<T>(event: Event<T>, listener: EventListener<T>) {
        const eventSymbol = event._symbol;
        const eventListeners = this.listeners.get(eventSymbol);
        if (eventListeners) {
            eventListeners.delete(listener);
            logger.debug("EventManager", `Listener unregistered for event: ${event.name}`);
        }
    }

    /**
     * Emits an event, triggering all its registered listeners.
     * Listeners are executed asynchronously and concurrently.
     *
     * @template T The type of the event's payload.
     * @param event The event to emit.
     * @param payload The data to pass to the listeners.
     */
    async emit<T>(event: Event<T>, payload: T): Promise<void> {
        // Prevent recursive logging
        if (event.name !== "core:log") {
            //logger.debug(`Emitting event: ${event.name}`);
        }

        const eventListeners = this.listeners.get(event._symbol);
        if (!eventListeners || eventListeners.size === 0) {
            return;
        }

        const listenerPromises: (Promise<void> | void)[] = [];

        for (const listener of eventListeners) {
            listenerPromises.push(listener(payload));
        }

        const results = await Promise.allSettled(listenerPromises);
        const errors = results
            .filter((result): result is PromiseRejectedResult => result.status === 'rejected')
            .map(result => result.reason);

        if (errors.length > 0) {
            for (const error of errors) {
                logger.error("EventManager", `Error during event execution for '${event.name}':`, error instanceof Error ? error : new Error(String(error)));
            }
        }
    }
}

/**
 * The singleton instance of the EventManager.
 */
export const eventManager = new EventManager();

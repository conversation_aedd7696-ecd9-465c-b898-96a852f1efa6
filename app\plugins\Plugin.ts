import type { Command } from "../commands/Command";
import { logger } from "../../shared";
import { eventManager } from "../events/EventManager";
import type { Event, EventListener } from "../events/Event";

/**
 * Represents the current state of a plugin.
 */
export enum PluginState {
    /** Plugin is not loaded */
    UNLOADED = "UNLOADED",
    /** Plugin is currently being loaded */
    LOADING = "LOADING",
    /** Plugin is loaded and active */
    LOADED = "LOADED",
    /** Plugin is currently being unloaded */
    UNLOADING = "UNLOADING",
    /** Plug<PERSON> encountered an error during loading/operation */
    ERROR = "ERROR",
}

/**
 * Interface defining plugin metadata.
 */
export interface PluginMetadata {
    /** Unique identifier for the plugin */
    readonly name: string;
    /** Semantic version of the plugin */
    readonly version: string;
    /** Human-readable description of the plugin */
    readonly description: string;
    /** Whether this plugin is locked and cannot be unloaded */
    readonly locked?: boolean;
}

/**
 * Result of a plugin lifecycle operation.
 */
export class PluginResult {
    constructor(public readonly success: boolean, public readonly message?: string, public readonly error?: Error) {}

    static success(message?: string): PluginResult {
        return new PluginResult(true, message);
    }

    static error(message?: string, error?: Error): PluginResult {
        return new PluginResult(false, message, error);
    }
}

/**
 * Base class that all plugins must extend.
 * Provides lifecycle management, command registration, and state tracking.
 */
export abstract class Plugin {
    /** Plugin metadata information */
    abstract readonly metadata: PluginMetadata;

    /** Current state of the plugin */
    private _state: PluginState = PluginState.UNLOADED;

    /** Commands provided by this plugin */
    private _commands: Command[] = [];

    /** Active event listeners registered by this plugin */
    private _activeListeners: Array<[Event<any>, EventListener<any>]> = [];

    /** Error that occurred during plugin operation, if any */
    private _lastError?: Error;

    /**
     * Gets the current state of the plugin.
     */
    get state(): PluginState {
        return this._state;
    }

    /**
     * Gets the commands provided by this plugin.
     */
    get commands(): readonly Command[] {
        return [...this._commands];
    }

    /**
     * Gets the last error that occurred, if any.
     */
    get lastError(): Error | undefined {
        return this._lastError;
    }

    /**
     * Sets the plugin state (internal use only).
     */
    protected setState(state: PluginState, error?: Error) {
        this._state = state;
        this._lastError = error;
        logger.debug(this.metadata.name, `State changed to: ${state}`);
    }

    /**
     * Registers a command with this plugin.
     * @param command The command to register.
     */
    protected registerCommand(command: Command) {
        if (this._commands.some((cmd) => cmd.name === command.name)) {
            logger.warn(this.metadata.name, `Command '${command.name}' is already registered`);
            return;
        }
        this._commands.push(command);
        logger.debug(this.metadata.name, `Provides command: ${command.name}`);
    }

    /**
     * Unregisters a command from this plugin.
     * @param commandName The name of the command to unregister.
     */
    protected unregisterCommand(commandName: string) {
        const index = this._commands.findIndex((cmd) => cmd.name === commandName);
        if (index >= 0) {
            this._commands.splice(index, 1);
            logger.debug(this.metadata.name, `Unregistered command '${commandName}'`);
        }
    }

    /**
     * Clears all registered commands from this plugin.
     */
    protected clearCommands() {
        this._commands.length = 0;
        logger.debug(this.metadata.name, `Cleared all commands`);
    }

    /**
     * Registers an event listener that will be automatically removed when the plugin is unloaded.
     * @param event The event to listen for.
     * @param listener The function to execute when the event is emitted.
     */
    protected registerListener<T>(event: Event<T>, listener: EventListener<T>) {
        eventManager.on(event, listener);
        this._activeListeners.push([event, listener]);
        logger.debug(this.metadata.name, `Registered listener for event: ${event.name}`);
    }

    /**
     * Unregisters all event listeners that were registered by this plugin.
     * This is called automatically when the plugin is unloaded.
     */
    private unregisterAllListeners() {
        for (const [event, listener] of this._activeListeners) {
            eventManager.off(event, listener);
        }
        this._activeListeners = [];
        logger.debug(this.metadata.name, `Unregistered all event listeners`);
    }

    /**
     * Called when the plugin is being loaded.
     * Override this method to perform plugin-specific initialization.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async onLoad(): Promise<PluginResult> {
        // Default implementation - plugins can override
        return PluginResult.success(`Loaded successfully`);
    }

    /**
     * Called when the plugin is being unloaded.
     * Override this method to perform plugin-specific cleanup.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async onUnload(): Promise<PluginResult> {
        // Default implementation - plugins can override
        return PluginResult.success(`Unloaded successfully`);
    }

    /**
     * Called when the plugin is being reloaded.
     * Default implementation calls onUnload followed by onLoad.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async onReload(): Promise<PluginResult> {
        const unloadResult = await this.onUnload();
        if (!unloadResult.success) {
            return unloadResult;
        }
        return await this.onLoad();
    }

    /**
     * Internal method to perform the loading process with state management.
     */
    async load(availablePlugins: Map<string, Plugin>): Promise<PluginResult> {
        if (this._state === PluginState.LOADED) {
            return PluginResult.error(`Already loaded`);
        }

        if (this._state === PluginState.LOADING) {
            return PluginResult.error(`Currently being loaded`);
        }

        try {
            this.setState(PluginState.LOADING);

            // Call the plugin's onLoad method
            const result = await this.onLoad();

            if (result.success) {
                this.setState(PluginState.LOADED);
                logger.info(this.metadata.name, result.message || `v${this.metadata.version} loaded successfully`);
            } else {
                const error = result.error || new Error(result.message || "Unknown error during plugin loading");
                this.setState(PluginState.ERROR, error);
                logger.error(this.metadata.name, `Failed to load: ${result.message}`, error);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.setState(PluginState.ERROR, err);
            const message = `Plugin '${this.metadata.name}' failed to load`;
            logger.error(this.metadata.name, message, err);
            return PluginResult.error(message, err);
        }
    }

    /**
     * Internal method to perform the unloading process with state management.
     */
    async unload(): Promise<PluginResult> {
        if (this._state === PluginState.UNLOADED) {
            return PluginResult.error(`Plugin '${this.metadata.name}' is already unloaded`);
        }

        if (this._state === PluginState.UNLOADING) {
            return PluginResult.error(`Plugin '${this.metadata.name}' is currently being unloaded`);
        }

        if (this.metadata.locked) {
            return PluginResult.error(`Plugin '${this.metadata.name}' is locked and cannot be unloaded`);
        }

        try {
            this.setState(PluginState.UNLOADING);

            // Call the plugin's onUnload method
            const result = await this.onUnload();

            if (result.success) {
                this.clearCommands(); // Clear all commands when unloading
                this.unregisterAllListeners(); // Clean up all event listeners
                this.setState(PluginState.UNLOADED);
                logger.info(this.metadata.name, result.message || `Unloaded successfully`);
            } else {
                const error = result.error || new Error(result.message || "Unknown error during plugin unloading");
                this.setState(PluginState.ERROR, error);
                logger.error(this.metadata.name, `Failed to unload: ${result.message}`, error);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.setState(PluginState.ERROR, err);
            const message = `Plugin '${this.metadata.name}' failed to unload`;
            logger.error(this.metadata.name, message, err);
            return PluginResult.error(message, err);
        }
    }

    /**
     * Internal method to perform the reloading process with state management.
     */
    async reload(availablePlugins: Map<string, Plugin>): Promise<PluginResult> {
        if (this.metadata.locked) {
            return PluginResult.error(`Plugin '${this.metadata.name}' is locked and cannot be reloaded`);
        }

        try {
            // Call the plugin's onReload method
            const result = await this.onReload();

            if (result.success) {
                this.setState(PluginState.LOADED);
                logger.info(this.metadata.name, result.message || `Reloaded successfully`);
            } else {
                const error = result.error || new Error(result.message || "Unknown error during plugin reloading");
                this.setState(PluginState.ERROR, error);
                logger.error(this.metadata.name, `Failed to reload: ${result.message}`, error);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.setState(PluginState.ERROR, err);
            const message = `Plugin '${this.metadata.name}' failed to reload`;
            logger.error(this.metadata.name, message, err);
            return PluginResult.error(message, err);
        }
    }
}

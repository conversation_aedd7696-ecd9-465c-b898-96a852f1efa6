import { ref, computed, readonly, nextTick } from "vue";

// Shared WebSocket state
const socket = ref<WebSocket | null>(null);
const connectionStatus = ref<"connecting" | "connected" | "offline" | "error">("connecting");
const systemInfo = ref<any>(null);
const isConnected = computed(() => connectionStatus.value === "connected");

// Terminal-specific reactive state
const logLines = ref<Array<{ level: string; message: string }>>([]);
const completions = ref<any[]>([]);
const completionIndex = ref(-1);
const rememberedSelection = ref<string | null>(null);

// Terminal UI state
const command = ref("");
const showCompletions = ref(true);
const lastCompletionInput = ref("");

// Terminal completion caching
const CACHE_DURATION = 30000; // 30 seconds
const STORAGE_KEY_COMPLETIONS = "chatbot-completions-cache";
const STORAGE_KEY_TIMESTAMPS = "chatbot-completions-timestamps";

// Initialize cache from localStorage
const initializeCompletionCache = (): { cache: Map<string, any[]>; timestamps: Map<string, number> } => {
    try {
        const cachedData = localStorage.getItem(STORAGE_KEY_COMPLETIONS);
        const timestampData = localStorage.getItem(STORAGE_KEY_TIMESTAMPS);

        const cache = new Map<string, any[]>();
        const timestamps = new Map<string, number>();

        if (cachedData && timestampData) {
            const parsedCache = JSON.parse(cachedData);
            const parsedTimestamps = JSON.parse(timestampData);

            for (const [key, value] of Object.entries(parsedCache)) {
                const timestamp = parsedTimestamps[key];
                if (timestamp && Date.now() - timestamp < CACHE_DURATION) {
                    cache.set(key, value as any[]);
                    timestamps.set(key, timestamp);
                }
            }
        }

        return { cache, timestamps };
    } catch (error) {
        console.warn("Failed to load completions cache from localStorage:", error);
        return { cache: new Map(), timestamps: new Map() };
    }
};

const { cache: initialCache, timestamps: initialTimestamps } = import.meta.client ? initializeCompletionCache() : { cache: new Map(), timestamps: new Map() };
const completionsCache = ref<Map<string, any[]>>(initialCache);
const cacheTimestamps = ref<Map<string, number>>(initialTimestamps);

// Track the last request to match with responses
let lastRequestedPartial = "";

// Memory reporting for terminal
let terminalMemoryReportInterval: NodeJS.Timeout | null = null;

// Phantom text computation
const phantomText = computed(() => {
    if (!command.value || completionIndex.value === -1 || !completions.value[completionIndex.value]) {
        return "";
    }

    const currentInput = command.value;
    const selectedCompletion = completions.value[completionIndex.value].name;

    // Only show phantom text if the completion starts with what we've typed
    if (selectedCompletion.toLowerCase().startsWith(currentInput.toLowerCase())) {
        return currentInput + selectedCompletion.substring(currentInput.length);
    }

    return "";
});

// Helper function to format aliases display
const formatAliases = (aliases?: string[]): string => {
    if (!aliases || aliases.length === 0) return "";
    return `(${aliases.join(", ")})`;
};

// Scroll to bottom helper
const scrollToBottom = (logContainer: HTMLElement | null) => {
    nextTick(() => {
        if (logContainer) {
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    });
};

// Scroll to active completion helper
const scrollToActiveCompletion = () => {
    nextTick(() => {
        if (completionIndex.value >= 0) {
            const grid = document.querySelector(".completions-grid");
            const activeItem = document.querySelector(".completion-active");
            if (grid && activeItem) {
                const gridRect = grid.getBoundingClientRect();
                const itemRect = activeItem.getBoundingClientRect();

                // Check if item is above visible area
                if (itemRect.top < gridRect.top) {
                    activeItem.scrollIntoView({ block: "start", behavior: "smooth" });
                }
                // Check if item is below visible area
                else if (itemRect.bottom > gridRect.bottom) {
                    activeItem.scrollIntoView({ block: "end", behavior: "smooth" });
                }
            }
        }
    });
};

const startTerminalMemoryReporting = () => {
    if (!import.meta.client) return;

    stopTerminalMemoryReporting();
    terminalMemoryReportInterval = setInterval(() => {
        if (socket.value && socket.value.readyState === WebSocket.OPEN) {
            const perfMemory = (performance as any)?.memory;
            if (perfMemory && perfMemory.usedJSHeapSize) {
                const frontendMemory = {
                    heapUsed: perfMemory.usedJSHeapSize,
                    heapTotal: perfMemory.totalJSHeapSize,
                    rss: perfMemory.usedJSHeapSize,
                    external: 0,
                };
                socket.value.send(JSON.stringify({ type: "memoryReport", payload: { memory: frontendMemory } }));
            }
        }
    }, 30000); // Every 30 seconds for terminal
};

const stopTerminalMemoryReporting = () => {
    if (terminalMemoryReportInterval) {
        clearInterval(terminalMemoryReportInterval);
        terminalMemoryReportInterval = null;
    }
};

// Cache validation helper
const isCacheValid = (key: string): boolean => {
    const timestamp = cacheTimestamps.value.get(key);
    if (!timestamp) return false;
    return Date.now() - timestamp < CACHE_DURATION;
};

// Message handlers for different components
const messageHandlers = new Set<(data: any) => void>();

// Uptime tracking
const uptime = ref<number | null>(null);
let baseUptime: number | null = null;
let baseTimestamp: number | null = null;
let uptimeTimer: NodeJS.Timeout | null = null;

// Helper function to parse uptime strings
const parseUptimeString = (uptimeStr: string): number | null => {
    try {
        let totalSeconds = 0;
        const daysMatch = uptimeStr.match(/(\d+)d/);
        if (daysMatch) totalSeconds += parseInt(daysMatch[1]) * 86400;
        const hoursMatch = uptimeStr.match(/(\d+)h/);
        if (hoursMatch) totalSeconds += parseInt(hoursMatch[1]) * 3600;
        const minutesMatch = uptimeStr.match(/(\d+)m/);
        if (minutesMatch) totalSeconds += parseInt(minutesMatch[1]) * 60;
        const secondsMatch = uptimeStr.match(/(\d+)s/);
        if (secondsMatch) totalSeconds += parseInt(secondsMatch[1]);
        return totalSeconds > 0 ? totalSeconds : null;
    } catch (error) {
        console.error("Error parsing uptime string:", uptimeStr, error);
        return null;
    }
};

const startUptimeTimer = () => {
    stopUptimeTimer();
    if (import.meta.client) {
        uptimeTimer = setInterval(() => {
            if (baseUptime !== null && baseTimestamp !== null) {
                const elapsedSeconds = Math.floor((Date.now() - baseTimestamp) / 1000);
                uptime.value = baseUptime + elapsedSeconds;
            }
        }, 1000);
    }
};

const stopUptimeTimer = () => {
    if (uptimeTimer) {
        clearInterval(uptimeTimer);
        uptimeTimer = null;
    }
};

const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m ${secs.toString().padStart(2, "0")}s`;
    if (minutes > 0) return `${minutes}m ${secs.toString().padStart(2, "0")}s`;
    return `${secs.toString().padStart(2, "0")}s`;
};

// Connection management
let reconnectTimer: NodeJS.Timeout | null = null;
let systemInfoInterval: NodeJS.Timeout | null = null;

const connect = () => {
    if (!import.meta.client) return;

    connectionStatus.value = "connecting";
    // TODO: Make this dynamic
    socket.value = new WebSocket("ws://localhost:4000");

    socket.value.onopen = () => {
        connectionStatus.value = "connected";
        console.log("WebSocket connected");

        // Clear terminal on reconnect
        logLines.value = [];

        // Request initial system info
        if (socket.value) {
            socket.value.send(JSON.stringify({ type: "systemInfo" }));
        }

        // Start terminal memory reporting
        startTerminalMemoryReporting();

        // Request initial completions for terminal
        requestCompletions("");

        // Set up periodic system info requests
        if (systemInfoInterval) {
            clearInterval(systemInfoInterval);
        }

        systemInfoInterval = setInterval(() => {
            if (socket.value && socket.value.readyState === WebSocket.OPEN) {
                // Send frontend memory report first if available
                const perfMemory = (performance as any)?.memory;
                if (perfMemory && perfMemory.usedJSHeapSize) {
                    const frontendMemory = {
                        heapUsed: perfMemory.usedJSHeapSize,
                        heapTotal: perfMemory.totalJSHeapSize,
                        rss: perfMemory.usedJSHeapSize,
                        external: 0,
                    };
                    socket.value!.send(JSON.stringify({ type: "memoryReport", payload: { memory: frontendMemory } }));
                }

                // Request system info after memory report
                setTimeout(() => {
                    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
                        socket.value.send(JSON.stringify({ type: "systemInfo" }));
                    }
                }, 100);
            }
        }, 5000);

        // Send periodic pings to keep connection alive
        setInterval(() => {
            if (socket.value && socket.value.readyState === WebSocket.OPEN) {
                socket.value.send(JSON.stringify({ type: "ping" }));
            }
        }, 30000);
    };

    socket.value.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);

            // Handle system info messages (for layout/overview/settings)
            if (data.type === "systemInfo") {
                systemInfo.value = data.payload;

                // Handle uptime updates
                let initialUptime = null;
                if (data.payload?.app?.uptimeSeconds) {
                    initialUptime = data.payload.app.uptimeSeconds;
                } else if (data.payload?.app?.uptime) {
                    const uptimeStr = data.payload.app.uptime;
                    initialUptime = parseUptimeString(uptimeStr);
                }

                if (initialUptime !== null) {
                    baseUptime = initialUptime;
                    baseTimestamp = Date.now();
                    uptime.value = initialUptime;
                    startUptimeTimer();
                }
            }

            // Handle terminal-specific messages
            else if (data.type === "history") {
                if (data.payload.entries && Array.isArray(data.payload.entries)) {
                    const historyEntries = data.payload.entries.map((entry: any) => ({
                        level: entry.level,
                        message: entry.message,
                    }));
                    logLines.value.push(...historyEntries);
                }
            } else if (data.type === "log") {
                logLines.value.push({ level: data.payload.level, message: data.payload.message });
            } else if (data.type === "commandResult") {
                logLines.value.push({ level: data.payload.status, message: data.payload.message });
            } else if (data.type === "completions") {
                const receivedCompletions = data.payload.completions || [];

                // Cache the completions using the last requested partial
                const cacheKey = lastRequestedPartial.trim();
                completionsCache.value.set(cacheKey, receivedCompletions);
                cacheTimestamps.value.set(cacheKey, Date.now());

                // Persist to localStorage
                if (import.meta.client) {
                    try {
                        const cacheObj = Object.fromEntries(completionsCache.value);
                        const timestampsObj = Object.fromEntries(cacheTimestamps.value);
                        localStorage.setItem(STORAGE_KEY_COMPLETIONS, JSON.stringify(cacheObj));
                        localStorage.setItem(STORAGE_KEY_TIMESTAMPS, JSON.stringify(timestampsObj));
                    } catch (error) {
                        console.warn("Failed to save completions cache to localStorage:", error);
                    }
                }

                completions.value = receivedCompletions;

                // Try to preserve the previously selected completion
                if (receivedCompletions.length > 0) {
                    let newIndex = 0;

                    // If we had a remembered selection, try to find it in the new completions
                    if (rememberedSelection.value) {
                        const foundIndex = receivedCompletions.findIndex((c: any) => c.name === rememberedSelection.value);
                        if (foundIndex !== -1) {
                            newIndex = foundIndex;
                        } else {
                            // Selection not found, clear remembered selection
                            rememberedSelection.value = null;
                        }
                    }

                    completionIndex.value = newIndex;
                } else {
                    completionIndex.value = -1;
                    rememberedSelection.value = null;
                }
            } else if (data.type === "clear") {
                logLines.value = [];
            }

            // Call all registered message handlers for custom processing
            messageHandlers.forEach((handler) => {
                try {
                    handler(data);
                } catch (error) {
                    console.error("Error in message handler:", error);
                }
            });
        } catch (error) {
            console.error("Failed to parse WebSocket message:", error);
        }
    };

    socket.value.onerror = (error) => {
        console.error("WebSocket error:", error);
        connectionStatus.value = "error";
        stopUptimeTimer();
    };

    socket.value.onclose = () => {
        connectionStatus.value = "offline";
        stopUptimeTimer();
        stopTerminalMemoryReporting();
        uptime.value = null;
        baseUptime = null;
        baseTimestamp = null;

        if (systemInfoInterval) {
            clearInterval(systemInfoInterval);
            systemInfoInterval = null;
        }

        // Reconnect after 3 seconds
        if (reconnectTimer) {
            clearTimeout(reconnectTimer);
        }
        reconnectTimer = setTimeout(connect, 3000);
    };
};

const disconnect = () => {
    if (socket.value) {
        socket.value.close();
        socket.value = null;
    }

    if (systemInfoInterval) {
        clearInterval(systemInfoInterval);
        systemInfoInterval = null;
    }

    if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
    }

    stopUptimeTimer();
};

// Computed status text
const connectionStatusText = computed(() => {
    switch (connectionStatus.value) {
        case "connecting":
            return "Connecting...";
        case "connected":
            return "Connected";
        case "offline":
            return "Offline";
        case "error":
            return "Error";
        default:
            return "Unknown";
    }
});

// Helper functions for terminal operations
const sendCommand = (commandText?: string, logContainer?: HTMLElement | null) => {
    const cmdToSend = commandText || command.value;
    if (cmdToSend.trim() && socket.value && socket.value.readyState === WebSocket.OPEN) {
        // Don't add command to log here - the server will handle it and send it back via history
        socket.value.send(JSON.stringify({ type: "command", payload: { command: cmdToSend } }));
        command.value = "";
        resetCompletionSelection();
        requestCompletions(""); // Request completions for empty input to show all available commands
        if (logContainer !== undefined) {
            scrollToBottom(logContainer);
        }
    }
};

const requestCompletions = (partial: string) => {
    const cacheKey = partial.trim();

    // Check cache first
    if (isCacheValid(cacheKey) && completionsCache.value.has(cacheKey)) {
        const cachedCompletions = completionsCache.value.get(cacheKey)!;
        completions.value = cachedCompletions;

        // Handle selection preservation for cached results
        if (cachedCompletions.length > 0) {
            let newIndex = 0;
            if (rememberedSelection.value) {
                const foundIndex = cachedCompletions.findIndex((c: any) => c.name === rememberedSelection.value);
                if (foundIndex !== -1) {
                    newIndex = foundIndex;
                } else {
                    rememberedSelection.value = null;
                }
            }
            completionIndex.value = newIndex;
        } else {
            completionIndex.value = -1;
            rememberedSelection.value = null;
        }
        return;
    }

    // Request from server if not cached or cache expired
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        lastRequestedPartial = partial; // Track the request
        socket.value.send(
            JSON.stringify({
                type: "getCompletions",
                payload: { partial },
            })
        );
    }
};

const clearTerminal = () => {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        socket.value.send(JSON.stringify({ type: "clear" }));
    }
    logLines.value = [];
};

const sendMessage = (message: any) => {
    if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        socket.value.send(JSON.stringify(message));
    }
};

const addMessageHandler = (handler: (data: any) => void) => {
    messageHandlers.add(handler);
    return () => messageHandlers.delete(handler); // Return cleanup function
};

// Terminal-specific helper functions
const resetCompletionSelection = () => {
    completionIndex.value = -1;
    rememberedSelection.value = null;
};

const updateCompletionIndex = (newIndex: number) => {
    if (newIndex >= 0 && newIndex < completions.value.length) {
        completionIndex.value = newIndex;
        rememberedSelection.value = completions.value[newIndex].name;
    }
};

const applyCompletion = (completion: any) => {
    command.value = completion.name + " ";
    resetCompletionSelection();
    // Request completions for the selected command to show subcommands
    requestCompletions(completion.name + " ");
};

// Keyboard handling for terminal
const handleKeyDown = (event: KeyboardEvent, logContainer: HTMLElement | null) => {
    if (event.key === "Tab") {
        event.preventDefault();

        if (showCompletions.value && completions.value.length > 0) {
            // Cycle to the next completion
            if (completionIndex.value < completions.value.length - 1) {
                completionIndex.value++;
            } else {
                completionIndex.value = 0;
            }
            command.value = completions.value[completionIndex.value].name;
            // Remember this selection
            rememberedSelection.value = completions.value[completionIndex.value].name;
            scrollToActiveCompletion();
        } else {
            // Request new completions
            const currentInput = command.value.trim();
            lastCompletionInput.value = currentInput;
            requestCompletions(currentInput);
        }
    } else if (event.key === "Escape") {
        resetCompletionSelection();
    } else if (event.key === "Enter") {
        resetCompletionSelection();
        sendCommand(command.value, logContainer);
    } else if (event.key === "ArrowUp" && showCompletions.value) {
        event.preventDefault();
        if (completionIndex.value > 0) {
            completionIndex.value--;
            command.value = completions.value[completionIndex.value].name;
            // Remember this selection
            rememberedSelection.value = completions.value[completionIndex.value].name;
            scrollToActiveCompletion();
        }
    } else if (event.key === "ArrowDown" && showCompletions.value) {
        event.preventDefault();
        if (completionIndex.value < completions.value.length - 1) {
            completionIndex.value++;
            command.value = completions.value[completionIndex.value].name;
            // Remember this selection
            rememberedSelection.value = completions.value[completionIndex.value].name;
            scrollToActiveCompletion();
        }
    } else {
        // Any other key requests new completions but preserves selection when possible
        // Don't reset selection immediately - let the new completions handler preserve it
        // Request completions for the current input after a short delay
        setTimeout(() => {
            requestCompletions(command.value.trim());
        }, 100);
    }
};

export const useWebSocket = () => {
    // Auto-connect if not already connected
    if (import.meta.client && (!socket.value || socket.value.readyState === WebSocket.CLOSED)) {
        connect();
    }

    return {
        // Connection state
        connectionStatus: readonly(connectionStatus),
        connectionStatusText,
        isConnected,

        // Data
        systemInfo: readonly(systemInfo),
        uptime: readonly(uptime),

        // Terminal data
        logLines: readonly(logLines),
        completions: readonly(completions),
        completionIndex: readonly(completionIndex),
        rememberedSelection: readonly(rememberedSelection),

        // Terminal UI state
        command,
        showCompletions: readonly(showCompletions),
        lastCompletionInput: readonly(lastCompletionInput),
        phantomText,

        // Terminal completion cache
        completionsCache: readonly(completionsCache),
        cacheTimestamps: readonly(cacheTimestamps),

        // Utilities
        formatUptime,
        formatAliases,
        scrollToBottom,
        scrollToActiveCompletion,

        // Terminal operations
        sendCommand,
        requestCompletions,
        clearTerminal,
        resetCompletionSelection,
        updateCompletionIndex,
        applyCompletion,
        handleKeyDown,
        addMessageHandler,
        sendMessage,

        // Manual control (for cleanup in onUnmounted)
        connect,
        disconnect,
    };
};

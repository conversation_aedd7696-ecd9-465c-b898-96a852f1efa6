<template>
    <div class="page-container">
        <PageHeader title="Terminal" subtitle="Execute commands and view real-time logs">
            <template #actions>
                <button class="btn" @click="clearTerminal">
                    <Icon name="lucide:trash-2" size="16" />
                    Clear
                </button>
            </template>
        </PageHeader>

        <div class="terminal-container glass">
            <div class="terminal-content">
                <div ref="logContainer" class="log-container">
                    <div v-for="(line, index) in logLines" :key="index" :class="['log-line', `log-${line.level?.toLowerCase()}`]">
                        <template v-if="line.level === 'COMMAND'">
                            <span class="prompt-echo">❯</span>
                            <span class="log-message">{{ line.message.substring(1).trim() }}</span>
                        </template>
                        <template v-else>
                            <span class="log-message">{{ line.message }}</span>
                        </template>
                    </div>
                </div>
                <div v-if="showCompletions" class="completions-panel">
                    <div class="completions-header">
                        <span class="completions-title">Available Commands</span>
                        <span class="completions-count">({{ completions.length }})</span>
                    </div>
                    <div class="completions-grid">
                        <TransitionGroup name="completion" tag="div" class="completions-transition-group" appear>
                            <div v-for="(completion, index) in completions" :key="completion.name"
                                :class="['completion-item', { 'completion-active': index === completionIndex }]" @click="applyCompletion(completion)">
                                <div class="completion-main">
                                    <span class="completion-name">{{ completion.name }}</span>
                                    <span v-if="completion.aliases && completion.aliases.length > 0" class="completion-aliases">
                                        {{ formatAliases(completion.aliases) }}
                                    </span>
                                </div>
                                <div class="completion-description">{{ completion.description }}</div>
                            </div>
                        </TransitionGroup>
                    </div>
                </div>
            </div>
            <div class="input-container">
                <span class="prompt">❯</span>
                <div class="input-wrapper">
                    <input v-model="command" type="text" class="command-input" autofocus @keydown="handleKeyDown($event, logContainer)" />
                    <div v-if="phantomText" class="phantom-text">
                        <span class="phantom-invisible">{{ command }}</span><span class="phantom-visible">{{ phantomText.substring(command.length) }}</span>
                    </div>
                </div>
                <button class="send-button" :disabled="!command.trim()" @click="sendCommand(command, logContainer)" title="Send command (Enter)">
                    <Icon name="lucide:send" size="16" />
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useWebSocket } from "~/composables/useWebSocket";

const logContainer = ref<HTMLDivElement | null>(null);

// Use the WebSocket composable
const {
    // Terminal data
    logLines,
    completions,
    completionIndex,
    command,
    showCompletions,
    phantomText,

    // Utilities
    formatAliases,
    scrollToBottom,

    // Terminal operations
    sendCommand,
    clearTerminal,
    applyCompletion,
    handleKeyDown,
    requestCompletions,

    // Connection control
    disconnect
} = useWebSocket();

// Auto-scroll to bottom when new messages arrive
const autoScrollToBottom = () => {
    scrollToBottom(logContainer.value);
};

// Watch for log changes and auto-scroll
let observer: MutationObserver | null = null;

onMounted(() => {
    // Focus the input when the component mounts
    const input = document.querySelector('.command-input') as HTMLInputElement;
    if (input) {
        input.focus();
    }

    // Set up auto-scroll observer
    if (logContainer.value) {
        observer = new MutationObserver(() => {
            autoScrollToBottom();
        });
        observer.observe(logContainer.value, {
            childList: true,
            subtree: true
        });
    }

    // Request initial completions
    requestCompletions("");
});

onUnmounted(() => {
    if (observer) {
        observer.disconnect();
    }
});
</script>

<style scoped>
/* Terminal specific styles */
.page-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.terminal-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
    min-height: 0;
}

.terminal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

.log-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-lg);
    display: flex;
    flex-direction: column;
    min-height: 0;
    font-family: var(--font-mono);
}

/* Custom Scrollbar */
.log-container::-webkit-scrollbar {
    width: 10px;
}

.log-line {
    margin-bottom: 4px;
    white-space: pre-wrap;
    word-break: break-word;
    display: flex;
    align-items: flex-start;
}

.log-info {
    color: var(--text-primary);
}

.log-okay {
    color: var(--color-success);
}

.log-warn {
    color: var(--color-warning);
}

.log-error {
    color: var(--color-error);
}

.log-fatal {
    color: var(--color-error);
}

.log-debug {
    color: var(--text-muted);
}

.log-command {
    color: #ffffff;
}

.log-line .prompt-echo {
    color: var(--color-success);
    margin-right: 0.5em;
}

.completions-panel {
    max-height: 180px;
    background: rgba(255, 255, 255, 0.04);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.completions-header {
    padding: var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 12px;
    font-weight: 600;
}

.completions-title {
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.3);
}

.completions-count {
    color: var(--text-muted);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.3);
    font-weight: 400;
}

.completions-grid {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: transparent;
    transition: all 0.2s ease;
}

.completions-transition-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1px;
}

.input-container {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 12px 12px;
}

.prompt {
    margin-right: 0.5em;
    color: var(--color-success);
    font-weight: 700;
    font-family: var(--font-mono);
}

.input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.command-input {
    width: 100%;
    background-color: transparent;
    border: none;
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: inherit;
    outline: none;
    padding: 5px 0;
    caret-color: var(--text-primary);
    position: relative;
    z-index: 2;
}

.phantom-text {
    position: absolute;
    left: 0;
    top: 5px;
    font-family: var(--font-mono);
    font-size: inherit;
    pointer-events: none;
    z-index: 1;
    white-space: pre;
}

.phantom-invisible {
    opacity: 0;
}

.phantom-visible {
    color: var(--text-muted);
    opacity: 0.5;
}

.send-button {
    background-color: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.4);
    border-radius: 6px;
    color: var(--color-success);
    cursor: pointer;
    padding: 8px 10px;
    margin-left: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 36px;
}

.send-button:hover:not(:disabled) {
    background-color: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.6);
    transform: translateY(-1px);
}

.send-button:active:not(:disabled) {
    transform: translateY(0);
}

.send-button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: rgba(117, 117, 117, 0.1);
    border-color: rgba(117, 117, 117, 0.2);
    color: var(--text-muted);
}

.send-button svg {
    transition: transform 0.2s ease;
}

.send-button:hover:not(:disabled) svg {
    transform: translateX(1px);
}

.completion-item {
    padding: 10px 14px;
    color: var(--text-primary);
    cursor: pointer;
    font-family: var(--font-mono);
    font-size: 13px;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    gap: 3px;
    border-radius: 0;
    min-height: 70px;
    max-height: 70px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.completion-item:hover,
.completion-active {
    background: rgba(76, 175, 80, 0.08);
    border-bottom-color: rgba(76, 175, 80, 0.3);
    color: #ffffff;
}

.completion-main {
    display: flex;
    align-items: center;
    gap: 8px;
}

.completion-name {
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.completion-aliases {
    font-size: 0.85em;
    color: var(--text-muted);
    font-style: italic;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.completion-description {
    font-size: 0.85em;
    color: var(--text-muted);
    line-height: 1.2;
    margin-left: 4px;
    font-family: var(--font-sans);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.completion-active .completion-name {
    color: var(--color-success);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7), 0 0 4px rgba(0, 0, 0, 0.4);
}

.completion-active .completion-description {
    color: rgba(238, 238, 238, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.3);
}

.completions-grid::-webkit-scrollbar {
    width: 8px;
}

.completions-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
}

.completions-grid::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.completions-grid::-webkit-scrollbar-thumb:hover {
    background-color: #454545;
}

/* Vue Transition Classes for Completion Items */
.completion-enter-active {
    transition: all 0.3s ease;
}

.completion-leave-active {
    transition: all 0.2s ease;
}

.completion-enter-from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
}

.completion-leave-to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
}

.completion-move {
    transition: transform 0.3s ease;
}
</style>
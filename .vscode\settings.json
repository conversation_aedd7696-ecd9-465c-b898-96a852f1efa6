{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/CVS": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true, ".idea": true, "node_modules": true, "package-lock.json": true, "**/node_modules": true, "**/.nuxt": true}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"package.json": ".env, .env.defaults, .giti<PERSON>re, nuxt.config.ts, tsconfig.json"}, "hide-files.files": []}
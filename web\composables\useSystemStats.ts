import { computed } from "vue";
import { useWebSocket } from "./useWebSocket";
import { useServices } from "./useServices";

// Helper function to format memory with appropriate units
const formatMemory = (mb: string | number): string => {
    const mbValue = typeof mb === "string" ? parseFloat(mb) : mb;
    if (mbValue >= 1024) {
        return `${(mbValue / 1024).toFixed(1)} GB`;
    }
    return `${mbValue.toFixed(1)} MB`;
};

// Generic stat processor interface
interface StatConfig {
    getValue: (info: any) => string | number | null;
    getTotal?: (info: any) => string | number | null;
    formatValue?: (value: any) => string;
    calculateProgress?: boolean;
}

// Create a generic stat processor
const createStat = (config: StatConfig) => {
    const { systemInfo } = useWebSocket();

    const value = computed(() => {
        if (!systemInfo.value) return "Loading...";

        const rawValue = config.getValue(systemInfo.value);
        if (rawValue === null || rawValue === undefined) return "N/A";

        return config.formatValue ? config.formatValue(rawValue) : String(rawValue);
    });

    const total = computed(() => {
        if (!systemInfo.value || !config.getTotal) return undefined;

        const rawTotal = config.getTotal(systemInfo.value);
        if (rawTotal === null || rawTotal === undefined) return undefined;

        return config.formatValue ? config.formatValue(rawTotal) : String(rawTotal);
    });

    const subtitle = computed(() => {
        return total.value ? `of ${total.value}` : undefined;
    });

    const progress = computed(() => {
        if (!config.calculateProgress || !systemInfo.value) return undefined;

        const rawValue = config.getValue(systemInfo.value);
        const rawTotal = config.getTotal?.(systemInfo.value);

        if (!rawValue || !rawTotal) return undefined;

        const valueNum = typeof rawValue === "string" ? parseFloat(rawValue) : rawValue;
        const totalNum = typeof rawTotal === "string" ? parseFloat(rawTotal) : rawTotal;

        return Math.round((valueNum / totalNum) * 100);
    });

    return {
        value,
        subtitle,
        progress,
    };
};

export const useSystemStats = () => {
    // Get service data
    const { stats: serviceStats } = useServices();

    // App Memory Stat
    const appMemory = createStat({
        getValue: (info) => {
            return info?.memory?.total?.rss || info?.memory?.backend?.rss || null;
        },
        getTotal: (info) => info?.memory?.system?.total || null,
        formatValue: formatMemory,
        calculateProgress: true,
    });

    // System Memory Stat
    const systemMemory = createStat({
        getValue: (info) => info?.memory?.system?.used || null,
        getTotal: (info) => info?.memory?.system?.total || null,
        formatValue: formatMemory,
        calculateProgress: true,
    });

    // Plugins Stat
    const plugins = createStat({
        getValue: (info) => info?.plugins?.loaded || null,
        getTotal: (info) => info?.plugins?.total || null,
        calculateProgress: true,
    });

    // Commands Stat
    const commands = createStat({
        getValue: (info) => info?.commands?.total || null,
    });

    // Connected Clients Stat
    const clients = createStat({
        getValue: (info) => info?.websocket?.connectedClients || null,
    });

    // Message History Stat
    const history = createStat({
        getValue: (info) => info?.websocket?.historyEntries || null,
        getTotal: (info) => info?.websocket?.maxHistorySize || null,
        calculateProgress: true,
    });

    // Service Stats
    const connectedServices = computed(() => {
        return {
            value: computed(() => String(serviceStats.value.connectedServices)),
            subtitle: computed(() => `of ${serviceStats.value.totalServices}`),
            progress: computed(() => {
                if (serviceStats.value.totalServices === 0) return 0;
                return Math.round((serviceStats.value.connectedServices / serviceStats.value.totalServices) * 100);
            })
        };
    });

    const totalServices = computed(() => {
        return {
            value: computed(() => String(serviceStats.value.totalServices)),
            subtitle: computed(() => undefined),
            progress: computed(() => undefined)
        };
    });

    const errorServices = computed(() => {
        return {
            value: computed(() => String(serviceStats.value.errorServices)),
            subtitle: computed(() => serviceStats.value.errorServices > 0 ? "services with errors" : undefined),
            progress: computed(() => undefined)
        };
    });

    // Uptime stat (special case)
    const { formatUptime, uptime } = useWebSocket();
    const uptimeStat = computed(() => {
        if (!uptime.value) return "Loading...";
        return formatUptime(uptime.value);
    });

    // Connection status
    const { connectionStatusText } = useWebSocket();

    return {
        appMemory,
        systemMemory,
        plugins,
        commands,
        clients,
        history,
        uptime: uptimeStat,
        connectionStatus: connectionStatusText,
        // Service stats
        connectedServices,
        totalServices,
        errorServices,
        // Helper functions
        formatMemory,
    };
};

{"name": "chatbotts-monorepo", "version": "1.0.0", "private": true, "type": "module", "workspaces": ["app", "web"], "scripts": {"app": "cd app && npm run dev", "web": "cd web && npm run dev", "app:build": "cd app && npm run build", "web:build": "cd web && npm run build", "clean": "rm -rf app/node_modules web/node_modules node_modules app/dist web/dist", "setup": "npm install && concurrently --prefix-colors \"cyan,magenta\" \"cd app && npm install\" \"cd web && npm install\"", "build": "concurrently --prefix-colors \"cyan,magenta\" \"cd app && npm run build\" \"cd web && npm run build\"", "dev": "concurrently --prefix-colors \"cyan,magenta\" \"cd app && npm run dev\" \"cd web && npm run dev\""}, "devDependencies": {"@types/node": "^24.0.10", "@types/ws": "^8.18.1", "concurrently": "^9.2.0", "dotenv-mono": "^1.3.14", "esbuild": "^0.25.5", "tsx": "^4.20.3", "typescript": "^5.8.3"}}
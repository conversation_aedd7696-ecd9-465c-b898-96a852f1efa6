:root {
    /* Colors */
    --bg-primary: #000000;
    --bg-secondary: rgba(255, 255, 255, 0.05);
    --bg-glass: rgba(255, 255, 255, 0.1);
    --border-color: rgba(255, 255, 255, 0.2);
    --border-hover: rgba(255, 255, 255, 0.3);

    --text-primary: #eaeaea;
    --text-secondary: #999999;
    --text-muted: #666666;

    --color-success: #4caf50;
    --color-warning: #ffc107;
    --color-error: #f44336;
    --color-info: #64b5f6;

    /* Service Brand Colors */
    --discord-color: #5865f2;
    --twitch-color: #9146ff;
    --youtube-color: #ff0000;
    --slack-color: #4a154b;
    --telegram-color: #26a5e4;

    /* Typography */
    --font-mono: "JetBrains Mono", "Consolas", "Monaco", monospace;
    --font-sans: "<PERSON>", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;

    /* Spacing */
    --space-xs: 4px;
    --space-sm: 8px;
    --space-md: 16px;
    --space-lg: 24px;
    --space-xl: 32px;

    /* Effects */
    --blur: blur(20px);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    --transition: all 0.2s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body, #__nuxt {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

body {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: 14px;
    line-height: 1.6;
    position: relative;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4), 0 0 3px rgba(0, 0, 0, 0.2);
}

/* Enhanced Background with Glass-friendly Pattern */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: radial-gradient(circle at 20% 80%, rgba(76, 175, 80, 0.08) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(100, 181, 246, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 193, 7, 0.04) 0%, transparent 50%), linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f0f0f 100%);
}

/* Animated Dot Pattern Overlay */
body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.02) 1px, transparent 0);
    background-size: 40px 40px;
    animation: patternMove 80s linear infinite;
}

@keyframes patternMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(40px, 40px);
    }
}

/* Floating Orbs for Extra Visual Interest */
.bg-orb {
    position: fixed;
    border-radius: 50%;
    opacity: 0.3;
    animation: float 12s ease-in-out infinite;
    z-index: -1;
    pointer-events: none;
    filter: blur(2px);
}

.bg-orb:nth-child(1) {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(76, 175, 80, 0.08) 0%, transparent 70%);
    top: 15%;
    left: 20%;
    animation-delay: 0s;
    animation-duration: 15s;
}

.bg-orb:nth-child(2) {
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(100, 181, 246, 0.06) 0%, transparent 70%);
    top: 65%;
    right: 25%;
    animation-delay: -5s;
    animation-duration: 18s;
}

.bg-orb:nth-child(3) {
    width: 140px;
    height: 140px;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.05) 0%, transparent 70%);
    bottom: 25%;
    left: 70%;
    animation-delay: -10s;
    animation-duration: 20s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-15px) translateX(8px);
        opacity: 0.5;
    }
}

/* Geometric Background Elements */
.bg-mesh {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.04;
    background-image: linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px), linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 80px 80px;
    animation: meshMove 120s linear infinite;
}

@keyframes meshMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(80px, 80px);
    }
}

.bg-squares {
    display: none;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
    border: 1px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
    background-clip: content-box;
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Liquid Glass Effects - Inspired by iOS 26 */
.glass {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 50%, rgba(255, 255, 255, 0.06) 100%);
    backdrop-filter: blur(12px) saturate(160%) brightness(115%);
    -webkit-backdrop-filter: blur(12px) saturate(160%) brightness(115%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Liquid Glass hover effects */
.glass:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.11) 0%, rgba(255, 255, 255, 0.04) 50%, rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.12), 0 3px 10px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.18), inset 0 -1px 0 rgba(255, 255, 255, 0.06);
    transform: translateY(-1px);
}

/* Specular highlight effect */
.glass::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.015) 50%, transparent 100%);
    transition: left 1s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.glass:hover::before {
    left: 100%;
}

/* Page Header Consistency */
.page-header {
    margin-bottom: var(--space-xl) !important;
}

.page-header h1,
.page-header h2 {
    font-size: 28px !important;
    font-weight: 700 !important;
    margin-bottom: var(--space-xs) !important;
}

/* Utility Classes */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

/* Common page layout styles */
.page-container {
    height: 100%;
    overflow-y: auto;
}

.content-grid {
    display: grid;
    gap: var(--space-lg);
}

.two-column-grid {
    grid-template-columns: 1fr 350px;
}

.auto-fit-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 250px));
}

.stats-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 325px));
    gap: var(--space-md);
    justify-content: start;
}

.services-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

@media (max-width: 1024px) {
    .two-column-grid {
        grid-template-columns: 1fr;
    }

    .auto-fit-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 220px));
    }
}

/* Scrollable containers - add right padding for scrollbar spacing */
.scrollable-content {
    padding-right: 16px;
}

.scrollable-content::-webkit-scrollbar {
    width: 6px;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-sm {
    gap: var(--space-sm);
}

.gap-md {
    gap: var(--space-md);
}

.gap-lg {
    gap: var(--space-lg);
}

/* Liquid Glass Button Styles */
.btn {
    padding: var(--space-sm) var(--space-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 50%, rgba(255, 255, 255, 0.06) 100%);
    backdrop-filter: blur(8px) saturate(140%) brightness(110%);
    -webkit-backdrop-filter: blur(8px) saturate(140%) brightness(110%);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.02) 50%, transparent 100%);
    transition: left 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.11) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.btn-primary {
    background: rgba(76, 175, 80, 0.15);
    border-color: rgba(76, 175, 80, 0.4);
    color: var(--color-success);
}

.btn-primary:hover {
    background: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.6);
}

.btn-error {
    background: rgba(244, 67, 54, 0.15);
    border-color: rgba(244, 67, 54, 0.4);
    color: var(--color-error);
}

.btn-error:hover {
    background: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.6);
}

/* Liquid Glass Action buttons */
.action-btn {
    padding: var(--space-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0.04) 100%);
    backdrop-filter: blur(10px) saturate(150%) brightness(112%);
    -webkit-backdrop-filter: blur(10px) saturate(150%) brightness(112%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 14px;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.action-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.012) 50%, transparent 100%);
    transition: left 0.9s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.action-btn:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.09) 0%, rgba(255, 255, 255, 0.03) 50%, rgba(255, 255, 255, 0.06) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn svg,
.action-btn .icon {
    color: var(--color-success);
}

/* @nuxt/icon components styling */
.icon {
    display: inline-block;
    vertical-align: middle;
    color: inherit;
}

/* Green color for specific icon containers */
.logo-icon .icon,
.uptime-icon .icon,
.action-btn .icon,
.stat-card .icon {
    color: var(--color-success);
}

/* Activity icon colors based on type */
.activity-icon.connect .icon {
    color: var(--color-success);
}

.activity-icon.message .icon {
    color: var(--color-info);
}

.activity-icon.error .icon {
    color: var(--color-error);
}

.activity-icon.success .icon {
    color: var(--color-success);
}

.activity-icon.info .icon {
    color: var(--color-warning);
}

/* Tab Styles */
.tabs {
    display: flex;
    gap: var(--space-xs);
    border-bottom: 1px solid var(--border-color);
    padding: 0 var(--space-lg);
}

.tab {
    padding: var(--space-md) var(--space-lg);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
    position: relative;
}

.tab:hover {
    color: var(--text-primary);
}

.tab.active {
    color: var(--text-primary);
    border-bottom-color: var(--color-success);
}

/* Card Styles */
.card {
    background: var(--bg-glass);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--space-lg);
    transition: var(--transition);
}

.card:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Service Card Styles */
.service-card {
    min-width: 300px;
    height: 400px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.service-card.discord {
    border-color: var(--discord-color);
}

.service-card.twitch {
    border-color: var(--twitch-color);
}

.service-card.youtube {
    border-color: var(--youtube-color);
}

.service-card.slack {
    border-color: var(--slack-color);
}

.service-card.telegram {
    border-color: var(--telegram-color);
}

/* Status Indicator */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-success);
}

.status-indicator.offline {
    background: var(--text-muted);
}

.status-indicator.error {
    background: var(--color-error);
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Enhanced Text Shadows for Glass Readability */
h1, h2, h3, h4, h5, h6 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.3);
}

.text-primary {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.text-secondary {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4), 0 0 3px rgba(0, 0, 0, 0.2);
}

.text-muted {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4), 0 0 3px rgba(0, 0, 0, 0.2);
}

/* Glass content needs stronger shadows */
.glass h1, .glass h2, .glass h3, .glass h4, .glass h5, .glass h6 {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7), 0 0 5px rgba(0, 0, 0, 0.4);
}

.glass p, .glass span, .glass div {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.glass .text-primary {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6), 0 0 3px rgba(0, 0, 0, 0.3);
}

.glass .text-secondary, .glass .text-muted {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

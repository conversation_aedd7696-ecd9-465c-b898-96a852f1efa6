<template>
    <div class="custom-select" v-click-outside="closeDropdown" ref="selectRef">
        <button class="select-button" @click="toggleDropdown" :class="{ 'open': isOpen }">
            <span class="select-value">{{ selectedOption?.label || placeholder }}</span>
            <Icon :name="isOpen ? 'lucide:chevron-up' : 'lucide:chevron-down'" size="16" class="select-arrow" />
        </button>

        <Teleport to="body">
            <div v-if="isOpen" class="select-dropdown" :style="dropdownStyle">
                <div class="select-option-group">
                    <div v-for="option in options" :key="option.value" class="select-option" :class="{ 'selected': option.value === modelValue }" @click="selectOption(option)">
                        {{ option.label }}
                    </div>
                </div>
            </div>
        </Teleport>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Option {
    value: string
    label: string
}

interface Props {
    modelValue: string
    options: Option[]
    placeholder?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
    'update:modelValue': [value: string]
}>()

const isOpen = ref(false)
const selectRef = ref<HTMLElement | null>(null)

const selectedOption = computed(() => {
    return props.options.find(option => option.value === props.modelValue)
})

const dropdownStyle = computed(() => {
    if (!selectRef.value || !isOpen.value) {
        return {}
    }

    const rect = selectRef.value.getBoundingClientRect()
    return {
        position: 'fixed' as const,
        top: `${rect.bottom + 4}px`,
        left: `${rect.left}px`,
        width: `${rect.width}px`,
        zIndex: '9999'
    }
})

const toggleDropdown = () => {
    isOpen.value = !isOpen.value
}

const closeDropdown = () => {
    isOpen.value = false
}

const selectOption = (option: Option) => {
    emit('update:modelValue', option.value)
    closeDropdown()
}

// Click outside directive
const vClickOutside = {
    mounted(el: any, binding: any) {
        el._clickOutside = (event: Event) => {
            if (!(el === event.target || el.contains(event.target as Node))) {
                binding.value()
            }
        }
        document.addEventListener('click', el._clickOutside)
    },
    unmounted(el: any) {
        document.removeEventListener('click', el._clickOutside)
    }
}
</script>

<style scoped>
.custom-select {
    position: relative;
    min-width: 150px;
}

.select-button {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    text-align: left;
}

.select-button:hover {
    border-color: var(--border-hover);
}

.select-button.open {
    border-color: var(--color-success);
}

.select-value {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.select-arrow {
    flex-shrink: 0;
    margin-left: var(--space-sm);
    color: var(--text-secondary);
    transition: var(--transition);
}

.select-dropdown {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.select-option-group {
    max-height: 200px;
    overflow-y: auto;
}

.select-option {
    padding: var(--space-sm) var(--space-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background: rgba(255, 255, 255, 0.08);
}

.select-option.selected {
    background: rgba(76, 175, 80, 0.15);
    border-bottom-color: rgba(76, 175, 80, 0.3);
    color: var(--color-success);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.3);
}

.select-option.selected:hover {
    background: rgba(76, 175, 80, 0.2);
}

/* Custom scrollbar for dropdown */
.select-option-group::-webkit-scrollbar {
    width: 6px;
}

.select-option-group::-webkit-scrollbar-track {
    background: #2a2a2a;
}

.select-option-group::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}
</style>
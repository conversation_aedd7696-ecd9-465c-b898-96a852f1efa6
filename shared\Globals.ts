/**
 * Provides access to global application constants and settings.
 */
export class Globals {
    /**
     * The name of the application.
     */
    static get NAME(): string {
        return process.env.APP_NAME || "ChatBot";
    }

    /**
     * The current version of the application.
     */
    static get VERSION(): string {
        return process.env.APP_VERSION || "1.0.0";
    }

    /**
     * Indicates whether the application is running in debug mode.
     * Debug mode is enabled based on the config file.
     */
    static get DEBUG_ENABLED(): boolean {
        return process.env.DEBUG === "true" || process.env.NODE_ENV === "development";
    }

    /**
     * The port for the web UI.
     */
    static get WEB_UI_PORT(): number {
        return process.env.WEB_UI_PORT ? parseInt(process.env.WEB_UI_PORT) : 3000;
    }

    /**
     * The port for the web socket server.
     */
    static get WEBSOCKET_PORT(): number {
        return process.env.WEBSOCKET_PORT ? parseInt(process.env.WEBSOCKET_PORT) : 4000;
    }

    // Private constructor to prevent instantiation, as this is a static utility class.
    private constructor() {}
}

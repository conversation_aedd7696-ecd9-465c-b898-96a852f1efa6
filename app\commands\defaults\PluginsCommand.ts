import { type Command, CommandResult } from "../Command";
import { pluginManager } from "../../plugins/PluginManager";
import { logger } from "../../../shared";
import { Plugin, PluginState } from "../../plugins/Plugin";
import * as path from "node:path";
import { readdir } from "node:fs/promises";
import { fileURLToPath } from "node:url";
import { dirname } from "node:path";

/**
 * A sub-command for listing all loaded plugins.
 * @implements {Command}
 */
class ListPluginsCommand implements Command {
    readonly name = "list";
    readonly description = "Lists all loaded plugins";

    /**
     * Executes the command to list all loaded plugins and their status.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     * @param _args - Command arguments (not used)
     * @param context - The context in which the command is being executed.
     */
    execute(_args: string[]): CommandResult {
        const plugins = pluginManager.getAllPlugins();
        if (plugins.length === 0) {
            return CommandResult.okay("No plugins are currently loaded");
        }

        logger.section("Loaded Plugins");
        plugins.forEach((plugin: Plugin) => {
            logger.keyValue(plugin.metadata.name, `Version: ${plugin.metadata.version} - Status: ${plugin.state}`, 15);
        });
        logger.newLine();
        return CommandResult.okay();
    }
}

/**
 * A sub-command for displaying detailed information about a specific plugin.
 * @implements {Command}
 */
class PluginInfoCommand implements Command {
    readonly name = "info";
    readonly description = "Shows detailed information about a specific plugin";
    readonly usage = "info <pluginName>";

    /**
     * Executes the command to show plugin details.
     * @param {string[]} args - The arguments for the command, expecting a plugin name.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     */
    execute(args: string[]): CommandResult {
        const pluginName = args[0];
        if (!pluginName) {
            return CommandResult.error("Plugin name is required. Usage: plugin info <pluginName>");
        }

        const plugin = pluginManager.getPlugin(pluginName);
        if (!plugin) {
            return CommandResult.error(`Plugin '${pluginName}' not found`);
        }

        logger.section(`Plugin Info: ${plugin.metadata.name}`);
        logger.keyValue("Version", plugin.metadata.version, 15);
        logger.keyValue("Description", plugin.metadata.description, 15);
        logger.keyValue("State", plugin.state, 15);
        logger.newLine();

        return CommandResult.okay();
    }
}

/**
 * A sub-command for loading a plugin from the file system.
 * @implements {Command}
 */
class LoadPluginCommand implements Command {
    readonly name = "load";
    readonly description = "Loads a plugin from the plugins directory";
    readonly usage = "load <pluginName>";

    /**
     * Executes the command to find and load a plugin.
     * @param {string[]} args - The arguments for the command, expecting a plugin name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const pluginName = args[0];
        if (!pluginName) {
            return CommandResult.error("Plugin name is required. Usage: plugin load <pluginName>");
        }

        if (pluginManager.getPlugin(pluginName)) {
            return CommandResult.error(`Plugin '${pluginName}' is already loaded`);
        }

        const __filename = fileURLToPath(import.meta.url);
        const __dirname = dirname(__filename);

        const pluginDirs = [
            path.resolve(__dirname, "../../plugins/defaults"),
            path.resolve(__dirname, "../../plugins")
        ];

        for (const dir of pluginDirs) {
            try {
                const files = await readdir(dir);
                for (const file of files) {
                    if (file.endsWith(".ts") && !file.endsWith(".d.ts")) {
                        const filePath = path.join(dir, file);
                        try {
                            const module = await import(filePath);
                            const PluginClass = module.default as (new () => Plugin) | undefined;

                            if (PluginClass && typeof PluginClass === "function" && PluginClass.prototype instanceof Plugin) {
                                const pluginInstance = new PluginClass();
                                if (pluginInstance.metadata.name === pluginName) {
                                    const result = await pluginManager.loadPlugin(pluginInstance);
                                    if (result.success) {
                                        return CommandResult.okay(`Plugin '${pluginName}' loaded successfully`);
                                    } else {
                                        return CommandResult.error(`Failed to load plugin '${pluginName}': ${result.message}`, result.error);
                                    }
                                }
                            }
                        } catch (e) {
                            // Ignore files that fail to import
                        }
                    }
                }
            } catch (e) {
                // Ignore directories that can't be read
            }
        }

        return CommandResult.error(`Plugin '${pluginName}' not found in any plugin directory`);
    }
}

/**
 * A sub-command for unloading an active plugin.
 * @implements {Command}
 */
class UnloadPluginCommand implements Command {
    readonly name = "unload";
    readonly description = "Unloads a plugin";
    readonly usage = "unload <pluginName>";

    /**
     * Executes the command to unload a plugin.
     * @param {string[]} args - The arguments for the command, expecting a plugin name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const pluginName = args[0];
        if (!pluginName) {
            return CommandResult.error("Plugin name is required. Usage: plugin unload <pluginName>");
        }

        try {
            const result = await pluginManager.unloadPlugin(pluginName);
            if(result.success) {
                return CommandResult.okay(`Unloaded successfully`);
            } else {
                return CommandResult.error(result.message, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to unload: ${err.message}`, err);
        }
    }
}

/**
 * A sub-command for reloading an active plugin.
 * @implements {Command}
 */
class ReloadPluginCommand implements Command {
    readonly name = "reload";
    readonly description = "Reloads a plugin";
    readonly usage = "reload <pluginName>";

    /**
     * Executes the command to reload a plugin.
     * @param {string[]} args - The arguments for the command, expecting a plugin name.
     * @returns {Promise<CommandResult>} A promise resolving to a result object.
     */
    async execute(args: string[]): Promise<CommandResult> {
        const pluginName = args[0];
        if (!pluginName) {
            return CommandResult.error("Plugin name is required. Usage: plugin reload <pluginName>");
        }

        try {
            const result = await pluginManager.reloadPlugin(pluginName);
            if(result.success) {
                return CommandResult.okay(`Plugin '${pluginName}' reloaded successfully`);
            } else {
                return CommandResult.error(result.message, result.error);
            }
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return CommandResult.error(`Failed to reload plugin '${pluginName}': ${err.message}`, err);
        }
    }
}

/**
 * The main command for managing plugins. It serves as a parent for various sub-commands.
 * If called without a sub-command, it displays help information for its sub-commands.
 * @implements {Command}
 */
export default class PluginsCommand implements Command {
    readonly name = "plugins";
    readonly aliases = ["plugin"];
    readonly description = "Manages plugins";
    readonly subCommands: Command[] = [
        new ListPluginsCommand(),
        new PluginInfoCommand(),
        new LoadPluginCommand(),
        new UnloadPluginCommand(),
        new ReloadPluginCommand(),
    ];

    /**
     * Executes the main plugin command.
     * Displays help information for the available sub-commands.
     * @param {string[]} args - The arguments for the command.
     * @returns {CommandResult} A result object indicating the outcome of the command.
     */
    execute(args: string[]): CommandResult {
        logger.section("Plugin Command Help");
        logger.blank("Use 'plugin <sub-command>' to manage plugins.");
        logger.newLine();

        this.subCommands.forEach(cmd => {
            let usage = cmd.usage ? cmd.usage : cmd.name;
            if (cmd.aliases && cmd.aliases.length > 0) {
                usage += ` (${cmd.aliases.join(", ")})`;
            }
            const description = cmd.description ? `${cmd.description} (Usage: plugin ${usage})` : `(Usage: plugin ${usage})`;
            logger.keyValue(cmd.name, description, 15);
        });

        logger.newLine();
        return CommandResult.okay();
    }
}

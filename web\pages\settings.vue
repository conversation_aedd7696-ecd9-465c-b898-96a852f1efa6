<template>
    <div class="page-container scrollable-content">
        <PageHeader title="Settings" subtitle="Configure your bot's behavior and preferences">
            <template #actions>
                <button class="btn btn-primary" @click="saveSettings">
                    <Icon name="lucide:save" size="16" />
                    Save Changes
                </button>
            </template>
        </PageHeader>

        <div class="settings-container">
            <!-- General Settings -->
            <div class="settings-section glass">
                <h2>General Settings</h2>
                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Bot Name</h3>
                            <p>The display name for your bot across all services</p>
                        </div>
                        <input v-model="settings.general.botName" type="text" class="setting-input" placeholder="My Awesome Bot" />
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Command Prefix</h3>
                            <p>The prefix used to trigger bot commands</p>
                        </div>
                        <input v-model="settings.general.commandPrefix" type="text" class="setting-input small" placeholder="!" />
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Auto-restart on Crash</h3>
                            <p>Automatically restart the bot if it crashes</p>
                        </div>
                        <label class="toggle">
                            <input v-model="settings.general.autoRestart" type="checkbox" />
                            <span class="toggle-slider"></span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Debug Mode</h3>
                            <p>Enable detailed logging for debugging</p>
                        </div>
                        <label class="toggle">
                            <input v-model="settings.general.debugMode" type="checkbox" />
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Performance Settings -->
            <div class="settings-section glass">
                <h2>Performance</h2>
                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Message Cache Size</h3>
                            <p>Number of messages to keep in memory</p>
                        </div>
                        <div class="setting-slider">
                            <input v-model="settings.performance.cacheSize" type="range" min="100" max="10000" step="100" class="slider" />
                            <span class="slider-value">{{ settings.performance.cacheSize }}</span>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Rate Limit Delay</h3>
                            <p>Minimum delay between API requests (ms)</p>
                        </div>
                        <div class="setting-slider">
                            <input v-model="settings.performance.rateLimitDelay" type="range" min="0" max="5000" step="100" class="slider" />
                            <span class="slider-value">{{ settings.performance.rateLimitDelay }}ms</span>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Worker Threads</h3>
                            <p>Number of worker threads for processing</p>
                        </div>
                        <CustomSelect v-model="settings.performance.workerThreads" :options="workerThreadOptions" placeholder="Select threads" />
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="settings-section glass">
                <h2>Security</h2>
                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Admin Users</h3>
                            <p>User IDs with admin privileges (comma-separated)</p>
                        </div>
                        <textarea v-model="settings.security.adminUsers" class="setting-textarea" placeholder="123456789, 987654321" rows="3"></textarea>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Blacklisted Words</h3>
                            <p>Words to automatically filter (one per line)</p>
                        </div>
                        <textarea v-model="settings.security.blacklistedWords" class="setting-textarea" placeholder="Enter words to filter..." rows="4"></textarea>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Enable Anti-Spam</h3>
                            <p>Automatically detect and prevent spam</p>
                        </div>
                        <label class="toggle">
                            <input v-model="settings.security.antiSpam" type="checkbox" />
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- API Keys -->
            <div class="settings-section glass danger-zone">
                <h2>API Keys</h2>
                <p class="section-warning">
                    <Icon name="lucide:alert-triangle" size="16" />
                    Keep these keys secure. Never share them publicly.
                </p>
                <div class="settings-group">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Discord Bot Token</h3>
                            <p>Your Discord bot's authentication token</p>
                        </div>
                        <div class="secret-input-wrapper">
                            <input v-model="settings.apiKeys.discord" :type="showDiscordToken ? 'text' : 'password'" class="setting-input secret" placeholder="Enter Discord token..." />
                            <button class="toggle-visibility" @click="showDiscordToken = !showDiscordToken">
                                <Icon :name="showDiscordToken ? 'lucide:eye-off' : 'lucide:eye'" size="16" />
                            </button>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Twitch Client ID</h3>
                            <p>Your Twitch application's client ID</p>
                        </div>
                        <div class="secret-input-wrapper">
                            <input v-model="settings.apiKeys.twitchClientId" :type="showTwitchClientId ? 'text' : 'password'" class="setting-input secret" placeholder="Enter Twitch client ID..." />
                            <button class="toggle-visibility" @click="showTwitchClientId = !showTwitchClientId">
                                <Icon :name="showTwitchClientId ? 'lucide:eye-off' : 'lucide:eye'" size="16" />
                            </button>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-info">
                            <h3>Twitch Client Secret</h3>
                            <p>Your Twitch application's client secret</p>
                        </div>
                        <div class="secret-input-wrapper">
                            <input v-model="settings.apiKeys.twitchSecret" :type="showTwitchSecret ? 'text' : 'password'" class="setting-input secret" placeholder="Enter Twitch client secret..." />
                            <button class="toggle-visibility" @click="showTwitchSecret = !showTwitchSecret">
                                <Icon :name="showTwitchSecret ? 'lucide:eye-off' : 'lucide:eye'" size="16" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="settings-section glass">
                <h2>System Information</h2>
                <div v-if="systemInfo" class="info-grid">
                    <div class="info-section">
                        <h3>Application</h3>
                        <div class="info-item">
                            <span class="info-label">Name:</span>
                            <span class="info-value">{{ systemInfo.app.name }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Version:</span>
                            <span class="info-value">{{ systemInfo.app.version }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Environment:</span>
                            <span class="info-value">{{ systemInfo.environment.nodeEnv }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Debug Mode:</span>
                            <span class="info-value">{{ systemInfo.environment.debugMode ? 'Enabled' : 'Disabled' }}</span>
                        </div>
                    </div>

                    <div class="info-section">
                        <h3>Runtime</h3>
                        <div class="info-item">
                            <span class="info-label">Node.js:</span>
                            <span class="info-value">{{ systemInfo.runtime.nodejs }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Platform:</span>
                            <span class="info-value">{{ systemInfo.runtime.platform }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Nuxt:</span>
                            <span class="info-value">{{ systemInfo.runtime.nuxt }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Vue:</span>
                            <span class="info-value">{{ systemInfo.runtime.vue }}</span>
                        </div>
                    </div>
                </div>
                <div v-else class="loading-state">
                    Loading system information...
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// Settings state
const settings = reactive({
    general: {
        botName: 'ChatBot',
        commandPrefix: '!',
        autoRestart: true,
        debugMode: false
    },
    performance: {
        cacheSize: 1000,
        rateLimitDelay: 500,
        workerThreads: '4'
    },
    security: {
        adminUsers: '',
        blacklistedWords: '',
        antiSpam: true
    },
    apiKeys: {
        discord: '',
        twitchClientId: '',
        twitchSecret: ''
    }
})

// Toggle states for password visibility
const showDiscordToken = ref(false)
const showTwitchClientId = ref(false)
const showTwitchSecret = ref(false)

// Options for custom dropdowns
const workerThreadOptions = [
    { value: '1', label: '1 Thread' },
    { value: '2', label: '2 Threads' },
    { value: '4', label: '4 Threads' },
    { value: '8', label: '8 Threads' }
]

// Use shared WebSocket connection
const { systemInfo } = useWebSocket()

const saveSettings = () => {
    // TODO: Implement saving settings to backend
    console.log('Saving settings:', settings)
    alert('Settings saved successfully!')
}

// Component lifecycle is handled by the shared WebSocket composable
</script>

<style scoped>
.settings-page {
    height: 100%;
    overflow-y: auto;
}

/* Settings specific styles */

.settings-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

/* Responsive grid for larger screens */
@media (min-width: 1200px) {
    .settings-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1600px) {
    .settings-container {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Ensure settings don't get too narrow */
.settings-section {
    min-width: 350px;
}

.settings-section {
    padding: var(--space-lg);
    border-radius: 12px;
}

.settings-section h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.section-warning {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md);
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    color: var(--color-warning);
    font-size: 14px;
    margin-bottom: var(--space-lg);
}

.settings-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-lg);
}

.setting-info {
    flex: 1;
}

.setting-info h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.setting-info p {
    font-size: 13px;
    color: var(--text-secondary);
}

.setting-input,
.setting-textarea {
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

.setting-input {
    width: 300px;
    flex-shrink: 0;
}

.danger-zone .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-sm);
}

.danger-zone .setting-input {
    width: 100%;
}

.secret-input-wrapper .setting-input {
    flex: 1;
    min-width: 0;
}

.setting-input.small {
    width: 100px;
}

.setting-textarea {
    width: 400px;
    resize: vertical;
    font-family: var(--font-mono);
    font-size: 13px;
}

.setting-input:hover,
.setting-textarea:hover {
    border-color: var(--border-hover);
}

.setting-input:focus,
.setting-textarea:focus {
    border-color: var(--color-success);
}

/* System Information Styles */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-xl);
}

.info-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.info-section h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-xs) 0;
    font-size: 13px;
}

.info-label {
    color: var(--text-secondary);
}

.info-value {
    font-family: var(--font-mono);
    color: var(--text-primary);
    font-weight: 500;
}

.loading-state {
    text-align: center;
    padding: var(--space-xl);
    color: var(--text-secondary);
    font-style: italic;
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 24px;
    transition: var(--transition);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
    background-color: var(--text-secondary);
    border-radius: 50%;
    transition: var(--transition);
}

.toggle input:checked+.toggle-slider {
    background: rgba(76, 175, 80, 0.15);
    backdrop-filter: blur(8px) saturate(120%);
    -webkit-backdrop-filter: blur(8px) saturate(120%);
    border-color: rgba(76, 175, 80, 0.4);
    box-shadow: 0 0 6px rgba(76, 175, 80, 0.3);
}

.toggle input:checked+.toggle-slider:before {
    background: var(--color-success);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transform: translateX(24px);
}

/* Slider */
.setting-slider {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.slider {
    width: 200px;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--color-success);
    border-radius: 50%;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--color-success);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.slider-value {
    min-width: 60px;
    text-align: right;
    font-weight: 600;
}

/* Secret Input */
.secret-input-wrapper {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    width: 100%;
    max-width: 100%;
}

.setting-input.secret {
    font-family: var(--font-mono);
}

.toggle-visibility {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: 4px;
    transition: var(--transition);
}

.toggle-visibility:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .setting-input,
    .setting-textarea {
        width: 100%;
    }
}
</style>
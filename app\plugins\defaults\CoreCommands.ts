import { Plugin, PluginResult, type PluginMetadata } from "../Plugin";

import HelpCommand from "../../commands/defaults/HelpCommand";
import PluginsCommand from "../../commands/defaults/PluginsCommand";
import ServiceCommand from "../../commands/defaults/ServiceCommand";

/**
 * Core Commands Plugin - Contains essential system commands.
 * This plugin is locked and cannot be unloaded to ensure basic functionality is always available.
 */
export default class CoreCommands extends Plugin {
    readonly metadata: PluginMetadata = {
        name: "CoreCommands",
        version: "1.0.0",
        description: "Essential system commands",
        locked: true,
    };

    /**
     * Plugin loading lifecycle method.
     * Registers all core commands when the plugin loads.
     */
    override async onLoad(): Promise<PluginResult> {
        try {
            // Register core commands
            this.registerCommand(new HelpCommand());
            this.registerCommand(new PluginsCommand());
            this.registerCommand(new ServiceCommand());
            return PluginResult.success(`Loaded successfully`);
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            return PluginResult.error(`Failed to load`, err);
        }
    }

    /**
     * Plugin unloading lifecycle method.
     * Since this is a locked plugin, this should never be called,
     * but we provide an implementation for completeness.
     */
    override async onUnload(): Promise<PluginResult> {
        return PluginResult.error(`Plugin '${this.metadata.name}' is locked and cannot be unloaded`);
    }

    /**
     * Plugin reloading lifecycle method.
     * Since this is a locked plugin, this should never be called,
     * but we provide an implementation for completeness.
     */
    override async onReload(): Promise<PluginResult> {
        return PluginResult.error(`Plugin '${this.metadata.name}' is locked and cannot be reloaded`);
    }
}

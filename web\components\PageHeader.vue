<template>
    <div class="page-header">
        <div class="header-text">
            <h1>{{ title }}</h1>
            <p v-if="subtitle" class="subtitle">{{ subtitle }}</p>
        </div>
        <div v-if="$slots.actions" class="header-actions">
            <slot name="actions"></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    title: string
    subtitle?: string
}

defineProps<Props>()
defineSlots<{
    actions(): any
}>()
</script>

<style scoped>
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
}

.header-text {
    flex: 1;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 16px;
    margin-top: var(--space-xs);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

/* Responsive header actions */
@media (max-width: 500px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .header-actions {
        justify-content: flex-end;
    }
}
</style>

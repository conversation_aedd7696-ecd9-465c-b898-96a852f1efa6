// Load environment variables first, before any other imports
import { dotenvLoad } from "dotenv-mono";

// Initialize dotenv-mono for monorepo environment management
dotenvLoad({
    depth: 4,
    priorities: {
      ".env.defaults": 1,
      ".env": 25,
    }
});

import { commandManager } from "./commands/CommandManager";
import { CoreEvents } from "./events/CoreEvents";
import { eventManager } from "./events/EventManager";
import { Globals } from "../shared";
import { logger } from "../shared";
import { pluginManager } from "./plugins/PluginManager";
import { serviceManager } from "./services/ServiceManager";
import { webSocketManager } from "./api/WebSocketManager";

/**
 * Represents the main ChatBot application class.
 * It encapsulates the entire application lifecycle, including initialization,
 * execution, and graceful shutdown.
 */
class ChatBot {
    /**
     * Initializes the ChatBot's components, particularly the CommandManager, PluginManager, and ServiceManager.
     * This method must be called and awaited before `run`.
     */
    async initialize() {
        logger.debug("Application", `Initializing components...`);

        // Initialize CommandManager first (for backward compatibility)
        await commandManager.initialize();

        // Initialize PluginManager which will load all plugins and their commands
        await pluginManager.initialize();

        // Initialize ServiceManager which will load and manage external service connections
        await serviceManager.initialize();

        logger.debug("Application", `Components initialized`);
    }

    /**
     * Starts the bot's core services and runs the main loop.
     * This method initiates the console manager for CLI input and starts the WebSocket server for the UI.
     * Assumes `initialize()` has been called and awaited.
     */
    async startAndRun() {
        // Start the WebSocket server
        webSocketManager.start(Globals.WEBSOCKET_PORT);
    }

    /**
     * Sets up global error handlers to ensure the application exits gracefully on
     * unhandled exceptions or promise rejections.
     */
    private setupErrorHandlers() {
        process.on("uncaughtException", (err) => {
            logger.error("Application", "A critical unexpected error occurred. The application will now terminate.", err);
            process.exit(1);
        });

        process.on("unhandledRejection", (reason, promise) => {
            logger.error("Application", "An unhandled promise rejection occurred. The application will now terminate.", reason instanceof Error ? reason : new Error(String(reason)));
            // For debugging, one might want to log the promise that was rejected
            // console.error('Unhandled Rejection at:', promise);
            process.exit(1);
        });
    }

    /**
     * The main entry point for starting the ChatBot application.
     * This method orchestrates the initialization, running, and error handling of the bot.
     */
    async start() {
        try {
            console.clear();
            logger.info("Application", `Starting ${Globals.NAME} v${Globals.VERSION}`);
            await eventManager.emit(CoreEvents.appStarting, undefined);
            this.setupErrorHandlers();

            await this.initialize();
            await eventManager.emit(CoreEvents.appReady, undefined);
            await this.startAndRun();
        } catch (error) {
            logger.error("Application", "A critical error occurred during bot execution, and the application cannot continue.", error instanceof Error ? error : new Error(String(error)));
            process.exit(1);
        }
    }
}

/**
 * Main asynchronous function to initialize and run the ChatBot.
 */
async function main() {
    const bot = new ChatBot();
    await bot.start();
}

// Execute the main function
main();

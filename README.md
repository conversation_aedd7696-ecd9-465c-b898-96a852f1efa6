# ChatBotTS

A powerful, extensible chatbot framework built with TypeScript, featuring a modular plugin system, customizable commands, and both console and web interfaces.

## ✨ Features

- **🔌 Plugin System**: Modular architecture with dynamic plugin loading
- **⚡ Command Framework**: Extensible command system with sub-commands and aliases
- **🌐 Dual Interface**: Console and modern web UI
- **📊 Real-time Monitoring**: WebSocket-based live updates and system statistics
- **🎨 Modern Web UI**: Beautiful dark glass morphism design
- **🔧 TypeScript**: Fully typed for better development experience
- **🏗️ Monorepo**: Organized workspace structure for backend and frontend

## 🏗️ Architecture

```
ChatBotTS/
│
├── app/                    # Backend application
│   ├── api/               # WebSocket server and API endpoints
│   ├── commands/          # Command system and implementations
│   ├── console/           # Console interface management
│   ├── logging/           # Custom logging system
│   ├── plugins/           # Plugin system
│   ├── events/            # Event handling
│   ├── index.ts           # Main application entry point
│   └── package.json       # Backend dependencies
├── web/                    # Frontend application
│   ├── components/        # Vue components
│   ├── pages/             # Nuxt pages
│   ├── layouts/           # Page layouts
│   ├── app.vue            # Main app component
│   └── package.json       # Frontend dependencies
└── package.json            # Workspace configuration
```

## ⚙️ Getting Started

### Prerequisites

Ensure you have [Node.js](https://nodejs.org/) (version 18 or higher) installed on your system.

### Installation

Clone the repository and install dependencies for all workspaces:

```bash
git clone https://github.com/xIGBClutchIx/ChatBotTS.git
cd ChatBotTS
npm install
```

### Usage

#### Run Both Apps Simultaneously

```bash
npm run dev
```

This will start both the backend (`app`) and frontend (`web`) with hot reloading enabled.

#### Run Individual Workspaces

**Backend only:**
```bash
npm run app
```

**Frontend only:**
```bash
npm run web
```

**Build for production:**
```bash
npm run app:build    # Build backend
npm run web:build    # Build frontend
```

### Available Commands

Once the backend is running, you can interact with it via:

1. **Console Mode:** Direct command-line interface
2. **Web Interface:** Open `http://localhost:3000` for the web UI
3. **WebSocket Connection:** Connect to `ws://localhost:4000` for real-time communication

Type `help` in either interface to see available commands.

## 🛠️ Development

The project uses npm workspaces for efficient dependency management and development workflows. Both the backend and frontend support hot reloading for rapid development.

### Adding New Commands

Commands should be added to `app/commands/defaults/` and follow the established patterns. The system will automatically discover and load new commands.

### Web Interface Development

The web interface is built with Nuxt 3 and Vue 3, providing a modern development experience with TypeScript support and hot module replacement.

## 🧩 Plugin System

The plugin system allows for easy extension of functionality:

- **Dynamic Loading**: Plugins are loaded at runtime
- **Command Registration**: Plugins can register their own commands
- **Event System**: Subscribe to and emit custom events
- **State Management**: Lifecycle management with loading/unloading

## 📝 Commands

The command system supports:

- **Aliases**: Multiple ways to call the same command
- **Sub-commands**: Hierarchical command structure
- **Tab Completion**: Smart completion in the web interface
- **Help System**: Automatic help generation

## 🌐 Web Interface

The web UI features:

- **Real-time Logs**: Live log streaming from the backend
- **System Statistics**: Memory usage, performance metrics
- **Service Management**: Monitor and control various services
- **Dark Theme**: Modern glass morphism design

## 🔧 Configuration

Configuration is handled through:

- **Environment Variables**: Runtime configuration
- **Plugin Configuration**: Per-plugin settings
- **Command Arguments**: Dynamic command parameters

## 📊 Monitoring

Built-in monitoring includes:

- **Memory Usage**: Backend and frontend memory tracking
- **Performance Metrics**: Command execution timing
- **WebSocket Statistics**: Connection and message metrics
- **System Information**: Platform and runtime details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Links

- **Repository**: https://github.com/xIGBClutchIx/ChatBotTS
- **Issues**: https://github.com/xIGBClutchIx/ChatBotTS/issues
- **Wiki**: https://github.com/xIGBClutchIx/ChatBotTS/wiki

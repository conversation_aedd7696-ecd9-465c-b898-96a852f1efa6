/**
 * Represents the status of a command execution.
 */
export enum CommandStatus {
    /** Command executed successfully, continue normal operation. */
    OKAY,
    /** Command executed successfully, and the application should exit gracefully. */
    EXIT,
    /** Command encountered an error during execution. */
    ERROR,
}

/**
 * Represents the result of a command execution.
 */
export class CommandResult {
    /** The status of the command execution. */
    status: CommandStatus;
    /** Optional message providing details about the command's outcome (e.g., success message, error description). */
    message?: string;
    /** Optional Error object, typically provided when the status is ERROR, containing more detailed error information. */
    error?: Error;

    /**
     * Constructs a new CommandResult instance.
     * @param status The status of the command execution.
     * @param message Optional message providing details about the command's outcome.
     * @param error Optional Error object, typically provided when the status is ERROR.
     */
    private constructor(status: CommandStatus, message?: string, error?: Error) {
        this.status = status;
        this.message = message;
        this.error = error;
    }

    /**
     * Creates a CommandResult with an OKAY status.
     * @param message Optional success message.
     * @returns A CommandResult instance with status OKAY.
     */
    static okay(message?: string): CommandResult {
        return new CommandResult(CommandStatus.OKAY, message);
    }

    /**
     * Creates a CommandResult with an ERROR status.
     * @param message Optional error message.
     * @param error Optional Error object containing more details.
     * @returns A CommandResult instance with status ERROR.
     */
    static error(message?: string, error?: Error): CommandResult {
        return new CommandResult(CommandStatus.ERROR, message, error);
    }
}

/**
 * Interface that all command classes must implement.
 */
export interface Command {
    /** The primary name of the command, used to invoke it. */
    readonly name: string;
    /** A brief description of what the command does. */
    readonly description: string;
    /** Optional detailed usage instructions for the command. */
    readonly usage?: string;
    /** Optional array of alternative names for the command. */
    readonly aliases?: string[];
    /** Optional array of sub-commands. */
    readonly subCommands?: Command[];
    /**
     * Executes the command's logic.
     * @param args - An array of string arguments passed to the command.
     * @returns A CommandResult object or a Promise resolving to a CommandResult.
     * @param context - The context in which the command is being executed.
     */
    execute(args: string[]): Promise<CommandResult> | CommandResult;
}

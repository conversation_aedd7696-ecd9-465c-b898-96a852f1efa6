import { Service, ServiceResult, type ServiceMetadata, type ServiceConfig } from "../Service";
import { logger } from "../../../shared";

/**
 * Mock service for testing the service system.
 * Simulates connection/disconnection without external dependencies.
 */
export default class MockService extends Service {

    readonly metadata: ServiceMetadata = {
        name: "MockService",
        version: "1.0.0",
        description: "Mock service for testing connection lifecycle",
        platform: "test",
        essential: false,
        requiresAuth: false,
    };

    private isSimulatedConnected = false;
    private startTime = Date.now();

    constructor(config: ServiceConfig = {}) {
        super({
            autoConnect: true,
            connectionTimeout: 5000,
            maxRetries: 3,
            retryDelay: 2000,
            ...config
        });
    }

    /**
     * Simulates connecting to an external service.
     */
    async onConnect(): Promise<ServiceResult> {
        this.isSimulatedConnected = true;
        this.startTime = Date.now();

        return ServiceResult.success("Connected to mock service", {
            connectedAt: new Date().toISOString(),
            mockPlatform: this.metadata.platform
        });
    }

    /**
     * Simulates disconnecting from an external service.
     */
    async onDisconnect(): Promise<ServiceResult> {
        this.isSimulatedConnected = false;
        this.startTime = 0;

        return ServiceResult.success("Disconnected from mock service", {
            disconnectedAt: new Date().toISOString()
        });
    }

    /**
     * Simulates sending a message to an external platform.
     */
    async onSendMessage(message: any): Promise<ServiceResult> {
        if (!this.isSimulatedConnected) {
            return ServiceResult.error("MockService is not connected");
        }

        logger.debug("MockService", `Simulating message send:`, message);

        const response = {
            messageId: this.generateMockId(),
            status: "sent",
            timestamp: new Date().toISOString(),
            originalMessage: message,
            echo: `MockService received: ${JSON.stringify(message)}`
        };

        logger.debug("MockService", `Message sent successfully`, response);

        return ServiceResult.success("Message sent to mock service", response);
    }

    /**
     * Simulates a health check.
     */
    override async onHealthCheck(): Promise<ServiceResult> {
        if (!this.isSimulatedConnected) {
            return ServiceResult.error("MockService is not connected");
        }

        const healthData = {
            status: "healthy",
            uptime: Date.now() - this.startTime,
            latency: 0,
            timestamp: new Date().toISOString(),
            version: this.metadata.version
        };

        return ServiceResult.success("MockService is healthy", healthData);
    }

    /**
     * Gets the current connection status.
     */
    isConnected(): boolean {
        return this.isSimulatedConnected;
    }

    /**
     * Generates a mock ID for testing.
     */
    private generateMockId(): string {
        return `mock_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }
}

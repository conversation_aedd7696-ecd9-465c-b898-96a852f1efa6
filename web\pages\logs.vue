<template>
    <div class="page-container">
        <PageHeader title="Logs" subtitle="Monitor system activities and debug issues">
            <template #actions>
                <button class="btn btn-primary">
                    <Icon name="lucide:download" size="16" />
                    Export Logs
                </button>
            </template>
        </PageHeader>

        <div class="filters-section glass">
            <div class="log-filters">
                <CustomSelect v-model="selectedLevel" :options="levelOptions" placeholder="All Levels" />
                <CustomSelect v-model="selectedService" :options="serviceOptions" placeholder="All Services" />
                <input v-model="searchQuery" type="text" placeholder="Search logs..." class="search-input" />
            </div>
        </div>

        <div class="logs-container glass">
            <div class="logs-header">
                <div class="logs-stats">
                    <span class="stat">
                        <strong>{{ filteredLogs.length }}</strong> logs
                    </span>
                    <span class="stat">
                        Last updated: <strong>{{ lastUpdated }}</strong>
                    </span>
                </div>
                <div class="logs-controls">
                    <label class="checkbox-label">
                        <input type="checkbox" v-model="autoScroll" />
                        Auto-scroll
                    </label>
                    <button class="btn" @click="clearLogs">Clear</button>
                </div>
            </div>

            <div ref="logsContent" class="logs-content">
                <template v-for="log in filteredLogs" :key="log.id">
                    <div class="log-entry">
                        <div class="log-main-content">
                            <div class="log-timestamp" :style="{ backgroundColor: getTimestampBg(log.level), borderColor: getTimestampBorder(log.level), color: getTimestampColor(log.level) }">{{ formatTimestamp(log.timestamp) }}</div>
                            <div class="log-service" :style="{ backgroundColor: getServiceIconBg(log.service) }">
                                <Icon :name="getServiceIcon(log.service)" size="16" :style="{ color: getServiceIconColor(log.service) }" />
                            </div>
                            <div class="log-message">{{ log.message }}</div>
                            <button class="log-expand" v-if="log.details" @click="toggleDetails(log.id)">
                                <Icon :name="expandedLogs.has(log.id) ? 'lucide:chevron-up' : 'lucide:chevron-down'" size="12" />
                            </button>
                        </div>
                        <div v-if="expandedLogs.has(log.id) && log.details" class="log-details-inline">
                            <pre>{{ log.details }}</pre>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

interface LogEntry {
    id: string
    timestamp: number
    level: 'debug' | 'info' | 'warn' | 'error'
    service: string
    message: string
    details?: string
}

// Mock log data - replace with real data from your API
const logs = ref<LogEntry[]>([
    {
        id: '1',
        timestamp: Date.now() - 5000,
        level: 'info',
        service: 'discord',
        message: 'Successfully connected to Discord gateway',
        details: 'Gateway URL: wss://gateway.discord.gg/?v=10&encoding=json\nSession ID: abc123\nResuming: false'
    },
    {
        id: '2',
        timestamp: Date.now() - 10000,
        level: 'error',
        service: 'twitch',
        message: 'Failed to authenticate with Twitch API',
        details: 'Error: Invalid access token\nStatus: 401 Unauthorized\nEndpoint: https://api.twitch.tv/helix/users'
    },
    {
        id: '3',
        timestamp: Date.now() - 15000,
        level: 'debug',
        service: 'system',
        message: 'Memory usage: 145MB / 512MB'
    },
    {
        id: '4',
        timestamp: Date.now() - 20000,
        level: 'warn',
        service: 'youtube',
        message: 'Rate limit approaching: 95% of quota used'
    },
    {
        id: '5',
        timestamp: Date.now() - 25000,
        level: 'info',
        service: 'slack',
        message: 'New message received in #general'
    }
])

const selectedLevel = ref('')
const selectedService = ref('')
const searchQuery = ref('')
const autoScroll = ref(true)
const expandedLogs = ref(new Set<string>())
const logsContent = ref<HTMLElement | null>(null)

// Options for custom dropdowns
const levelOptions = [
    { value: '', label: 'All Levels' },
    { value: 'debug', label: 'Debug' },
    { value: 'info', label: 'Info' },
    { value: 'warn', label: 'Warning' },
    { value: 'error', label: 'Error' }
]

const serviceOptions = [
    { value: '', label: 'All Services' },
    { value: 'discord', label: 'Discord' },
    { value: 'twitch', label: 'Twitch' },
    { value: 'youtube', label: 'YouTube' },
    { value: 'slack', label: 'Slack' },
    { value: 'system', label: 'System' }
]

const lastUpdated = computed(() => {
    const now = new Date()
    return now.toLocaleTimeString()
})

const filteredLogs = computed(() => {
    return logs.value.filter(log => {
        if (selectedLevel.value && log.level !== selectedLevel.value) return false
        if (selectedService.value && log.service !== selectedService.value) return false
        if (searchQuery.value && !log.message.toLowerCase().includes(searchQuery.value.toLowerCase())) return false
        return true
    })
})

const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
    })
}

const getServiceIcon = (service: string) => {
    const serviceIcons: Record<string, string> = {
        discord: 'simple-icons:discord',
        twitch: 'simple-icons:twitch',
        youtube: 'simple-icons:youtube',
        slack: 'simple-icons:slack',
        system: 'lucide:cpu',
        api: 'lucide:globe',
        database: 'lucide:database',
        auth: 'lucide:shield-check',
        websocket: 'lucide:wifi'
    }
    return serviceIcons[service] || 'lucide:activity'
}

const getServiceIconColor = (service: string) => {
    const serviceColors: Record<string, string> = {
        discord: '#5865f2',
        twitch: '#9146ff',
        youtube: '#ff0000',
        slack: '#e01e5a',
        system: '#64b5f6',
        api: '#4caf50',
        database: '#ff9800',
        auth: '#e91e63',
        websocket: '#00bcd4'
    }
    return serviceColors[service] || '#81c784'
}

const getServiceIconBg = (service: string) => {
    const serviceBgs: Record<string, string> = {
        discord: 'rgba(88, 101, 242, 0.15)',
        twitch: 'rgba(145, 70, 255, 0.15)',
        youtube: 'rgba(255, 0, 0, 0.15)',
        slack: 'rgba(224, 30, 90, 0.15)',
        system: 'rgba(100, 181, 246, 0.15)',
        api: 'rgba(76, 175, 80, 0.15)',
        database: 'rgba(255, 152, 0, 0.15)',
        auth: 'rgba(233, 30, 99, 0.15)',
        websocket: 'rgba(0, 188, 212, 0.15)'
    }
    return serviceBgs[service] || 'rgba(129, 199, 132, 0.15)'
}

const getAdaptiveGlassBackground = (level: 'debug' | 'info' | 'warn' | 'error', service: string) => {
    const levelColorMix: Record<string, string> = {
        debug: '168, 85, 255',
        info: '163, 230, 53',
        warn: '224, 213, 97',
        error: '239, 68, 68'
    }

    const serviceColorMix: Record<string, string> = {
        discord: '88, 101, 242',
        twitch: '145, 70, 255',
        youtube: '255, 0, 0',
        slack: '224, 30, 90',
        system: '100, 181, 246',
        api: '76, 175, 80',
        database: '255, 152, 0',
        auth: '233, 30, 99',
        websocket: '0, 188, 212'
    }

    const levelRgb = levelColorMix[level] || '129, 199, 132'
    const serviceRgb = serviceColorMix[service] || '129, 199, 132'

    return `linear-gradient(135deg,
        rgba(${levelRgb}, 0.08) 0%,
        rgba(255, 255, 255, 0.02) 25%,
        rgba(${serviceRgb}, 0.06) 50%,
        rgba(255, 255, 255, 0.01) 75%,
        rgba(${levelRgb}, 0.04) 100%
    )`
}

const getTimestampBg = (level: 'debug' | 'info' | 'warn' | 'error') => {
    const levelBgs: Record<string, string> = {
        debug: 'rgba(168, 85, 255, 0.15)',
        info: 'rgba(163, 230, 53, 0.15)',
        warn: 'rgba(224, 213, 97, 0.15)',
        error: 'rgba(239, 68, 68, 0.15)'
    }
    return levelBgs[level] || 'rgba(163, 230, 53, 0.15)'
}

const getTimestampBorder = (level: 'debug' | 'info' | 'warn' | 'error') => {
    const levelBorders: Record<string, string> = {
        debug: 'rgba(168, 85, 255, 0.3)',
        info: 'rgba(163, 230, 53, 0.3)',
        warn: 'rgba(224, 213, 97, 0.3)',
        error: 'rgba(239, 68, 68, 0.3)'
    }
    return levelBorders[level] || 'rgba(163, 230, 53, 0.3)'
}

const getTimestampColor = (level: 'debug' | 'info' | 'warn' | 'error') => {
    const levelColors: Record<string, string> = {
        debug: '#c084fc',
        info: '#a3e635',
        warn: '#e0d561',
        error: '#ff5555'
    }
    return levelColors[level] || '#a3e635'
}

const toggleDetails = (logId: string) => {
    if (expandedLogs.value.has(logId)) {
        expandedLogs.value.delete(logId)
    } else {
        expandedLogs.value.add(logId)
    }
}

const clearLogs = () => {
    logs.value = []
    expandedLogs.value.clear()
}

// Auto-scroll to bottom when new logs are added
watch(() => logs.value.length, () => {
    if (autoScroll.value && logsContent.value) {
        nextTick(() => {
            if (logsContent.value) {
                logsContent.value.scrollTop = logsContent.value.scrollHeight
            }
        })
    }
})

// Simulate real-time logs
setInterval(() => {
    const services = ['discord', 'twitch', 'youtube', 'slack', 'system']
    const levels: LogEntry['level'][] = ['debug', 'info', 'warn', 'error']
    const messages = [
        'Processing user command',
        'Cache updated successfully',
        'API request completed',
        'Connection established',
        'Scheduled task executed',
        'Configuration reloaded',
        'User joined channel',
        'Message sent successfully'
    ]

    const newLog: LogEntry = {
        id: Date.now().toString(),
        timestamp: Date.now(),
        level: levels[Math.floor(Math.random() * levels.length)],
        service: services[Math.floor(Math.random() * services.length)],
        message: messages[Math.floor(Math.random() * messages.length)]
    }

    logs.value.push(newLog)

    // Keep only last 1000 logs
    if (logs.value.length > 1000) {
        logs.value.shift()
    }
}, 3000)
</script>

<style scoped>
/* Logs page specific styles */
.page-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.filters-section {
    margin-bottom: var(--space-lg);
    padding: var(--space-md) var(--space-lg);
    border-radius: 16px;
}

.log-filters {
    display: flex;
    gap: var(--space-md);
    flex: 1;
}

.search-input {
    padding: var(--space-sm) var(--space-md);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(255, 255, 255, 0.04) 100%);
    backdrop-filter: blur(8px) saturate(140%) brightness(110%);
    -webkit-backdrop-filter: blur(8px) saturate(140%) brightness(110%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: var(--text-primary);
    font-family: var(--font-sans);
    font-size: 14px;
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5), 0 0 3px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.search-input:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 50%, rgba(255, 255, 255, 0.05) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.search-input:focus {
    border-color: rgba(76, 175, 80, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 0 0 3px rgba(76, 175, 80, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.logs-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    overflow: hidden;
    min-height: 0;
    /* Allow flex child to shrink */
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.3) 100%);
    backdrop-filter: blur(8px) saturate(140%);
    -webkit-backdrop-filter: blur(8px) saturate(140%);
}

.logs-stats {
    display: flex;
    gap: var(--space-lg);
    font-size: 13px;
    color: var(--text-secondary);
}

.logs-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 14px;
    cursor: pointer;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.08);
}

.checkbox-label input[type="checkbox"]:checked {
    background: rgba(76, 175, 80, 0.15);
    border-color: rgba(76, 175, 80, 0.4);
    box-shadow: 0 0 6px rgba(76, 175, 80, 0.3);
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-success);
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.logs-content {
    flex: 1;
    overflow-y: auto;
    font-family: var(--font-mono);
    font-size: 13px;
}

.log-entry {
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 1px var(--space-sm);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
}

.log-main-content {
    display: grid;
    grid-template-columns: auto 60px 1fr auto;
    gap: var(--space-md);
    padding: var(--space-md) var(--space-lg);
    align-items: center;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
}

.log-timestamp {
    font-size: 12px;
    font-weight: 500;
    font-family: var(--font-mono);
    padding: 4px 8px;
    border-radius: 6px;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.log-service {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    padding: 6px;
}

.log-message {
    color: rgba(255, 255, 255, 0.6);
    word-break: break-word;
    font-weight: 400;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.log-expand {
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: 4px;
    transition: var(--transition);
}

.log-expand:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.log-details-inline {
    padding: var(--space-sm) var(--space-lg);
    margin: var(--space-sm) var(--space-lg) var(--space-sm) var(--space-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
    animation: slideDown 0.3s ease-out;
    transform-origin: top;
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        max-height: 500px;
        opacity: 1;
        transform: translateY(0);
    }
}

.log-details-inline pre {
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    white-space: pre-wrap;
    line-height: 1.4;
}

/* Log level specific colors - removed border-left since we have full background now */

@media (max-width: 768px) {
    .log-filters {
        flex-direction: column;
        gap: var(--space-md);
    }

    .search-input {
        min-width: unset;
        max-width: unset;
        width: 100%;
    }

    .log-main-content {
        grid-template-columns: auto 40px 1fr auto;
        padding: var(--space-sm);
        gap: var(--space-sm);
    }

    .log-timestamp {
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .filters-section {
        padding: var(--space-sm);
    }

    .log-main-content {
        grid-template-columns: auto 30px 1fr;
        padding: var(--space-xs);
        font-size: 12px;
    }

    .log-expand {
        display: none;
    }
}
</style>
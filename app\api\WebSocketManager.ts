import { commandManager } from "../commands/CommandManager";
import { CommandStatus, type CommandResult } from "../commands/Command";
import { CoreEvents } from "../events/CoreEvents";
import { eventManager } from "../events/EventManager";
import { logger } from "../../shared";
import { WebSocketServer, WebSocket, type RawData } from "ws";
import { createServer } from "http";
import type { IncomingMessage } from "http";
import { Globals } from "../../shared";
import { pluginManager } from "../plugins/PluginManager";
import { serviceManager } from "../services/ServiceManager";
import type { Service } from "../services/Service";
import * as os from "node:os";

interface WebSocketMessage {
    type: "command" | "log" | "event" | "history" | "getCompletions" | "clear" | "memoryReport" | "systemInfo" | "serviceUpdate" | "serviceData" | "getServices";
    payload: any;
}

interface FrontendMemoryUsage {
    heapUsed: number;
    heapTotal: number;
    rss: number;
    external: number;
    timestamp: number;
}

interface HistoryEntry {
    type: "log" | "commandResult";
    level: string;
    message: string;
    timestamp: number;
}

interface CompletionItem {
    name: string;
    description: string;
    aliases?: string[];
    usage?: string;
    isSubCommand?: boolean;
    parentCommand?: string;
}

/**
 * Manages the WebSocket server for real-time communication with the Web UI.
 */
export class WebSocketManager {
    private clients: Map<WebSocket, string> = new Map(); // Track client IDs
    private chatHistory: HistoryEntry[] = [];
    private readonly maxHistorySize = 1000; // Keep last 1000 entries

    // Frontend memory tracking
    private frontendMemory: FrontendMemoryUsage | null = null;

    // Security: Rate limiting
    private rateLimitMap = new Map<string, { count: number; resetTime: number }>();
    private readonly maxCommandsPerMinute = 30;
    private readonly rateLimitWindowMs = 60 * 1000; // 1 minute
    private cleanupTimer: NodeJS.Timeout | null = null;

    // Security: Allowed origins
    private readonly allowedOrigins = [
        `http://localhost:${Globals.WEB_UI_PORT}`,
        `http://127.0.0.1:${Globals.WEB_UI_PORT}`,
        `http://localhost:${Globals.WEB_UI_PORT + 1}`, // In case of port conflicts
        `http://127.0.0.1:${Globals.WEB_UI_PORT + 1}`
    ];

    /**
     * Starts the WebSocket server.
     * @param port The port to listen on.
     */
    start(port: number) {
        // Create HTTP server
        const httpServer = createServer();

        // Create WebSocket server
        const wss = new WebSocketServer({
            server: httpServer,
            verifyClient: (info: { origin: string; secure: boolean; req: IncomingMessage }) => {
                // Security: Origin validation
                const origin = info.origin;
                if (origin && !this.allowedOrigins.includes(origin)) {
                    logger.warn("WebSocket", `Connection rejected: Invalid origin ${origin}`);
                    return false;
                }
                return true;
            }
        });

        wss.on('connection', (ws, req) => {
            // Generate unique client ID
            const clientIP = req.socket.remoteAddress || 'unknown';
            const clientId = `${clientIP}:${Date.now()}:${Math.random()}`;
            this.clients.set(ws, clientId);

            // Send the chat history first
            if (this.chatHistory.length > 0) {
                ws.send(JSON.stringify({
                    type: "history",
                    payload: { entries: this.chatHistory }
                }));
            }

            // Then send the connected event
            ws.send(JSON.stringify({ type: "event", payload: { name: "connected" } }));

            // Handle messages
            ws.on('message', (message) => {
                this.handleMessage(ws, message);
            });

            // Handle close
            ws.on('close', () => {
                this.clients.delete(ws);
            });
        });

        // Start the HTTP server
        httpServer.listen(port, () => {
            logger.debug("WebSocket", `Server listening on ws://localhost:${port}`);
        });

        // Start periodic cleanup of rate limit map
        this.cleanupTimer = setInterval(() => {
            this.cleanupRateLimitMap();
        }, this.rateLimitWindowMs);

        // Subscribe to log events to forward them to clients and store in history
        eventManager.on(CoreEvents.log, (log) => {
            this.addToHistory("log", log.level, log.message);
            this.broadcast({ type: "log", payload: log });
        });

        // Subscribe to service events to broadcast service state changes
        eventManager.on(CoreEvents.serviceConnected, (service: Service) => {
            this.broadcast({
                type: "serviceUpdate",
                payload: {
                    event: "connected",
                    service: this.serializeService(service)
                }
            });
        });

        eventManager.on(CoreEvents.serviceDisconnected, (service: Service) => {
            this.broadcast({
                type: "serviceUpdate",
                payload: {
                    event: "disconnected",
                    service: this.serializeService(service)
                }
            });
        });

        eventManager.on(CoreEvents.serviceConnecting, (service: Service) => {
            this.broadcast({
                type: "serviceUpdate",
                payload: {
                    event: "connecting",
                    service: this.serializeService(service)
                }
            });
        });

        eventManager.on(CoreEvents.serviceReconnecting, (service: Service) => {
            this.broadcast({
                type: "serviceUpdate",
                payload: {
                    event: "reconnecting",
                    service: this.serializeService(service)
                }
            });
        });

        eventManager.on(CoreEvents.serviceError, (data: { service: Service; error: Error }) => {
            this.broadcast({
                type: "serviceUpdate",
                payload: {
                    event: "error",
                    service: this.serializeService(data.service),
                    error: data.error.message
                }
            });
        });
    }

    /**
     * Adds an entry to the chat history with automatic cleanup.
     */
    private addToHistory(type: "log" | "commandResult", level: string, message: string) {
        const entry: HistoryEntry = {
            type,
            level,
            message,
            timestamp: Date.now()
        };

        this.chatHistory.push(entry);

        // Keep only the most recent entries
        if (this.chatHistory.length > this.maxHistorySize) {
            this.chatHistory = this.chatHistory.slice(-this.maxHistorySize);
        }
    }

    /**
     * Clears the chat history.
     */
    clearHistory() {
        this.chatHistory = [];
    }

    /**
     * Gets the number of connected WebSocket clients.
     * @returns The number of active connections.
     */
    getConnectedClientsCount(): number {
        return this.clients.size;
    }

    /**
     * Gets the current chat history size.
     * @returns The number of entries in chat history.
     */
    getHistorySize(): number {
        return this.chatHistory.length;
    }

    /**
     * Gets information about the WebSocket server status.
     * @returns Server status information.
     */
    getServerInfo() {
        return {
            port: Globals.WEBSOCKET_PORT,
            connectedClients: this.getConnectedClientsCount(),
            historyEntries: this.getHistorySize(),
            maxHistorySize: this.maxHistorySize,
            rateLimitedClients: this.rateLimitMap.size,
            maxCommandsPerMinute: this.maxCommandsPerMinute
        };
    }

    /**
     * Gets the latest frontend memory usage information.
     * @returns Frontend memory usage or null if not available.
     */
    getFrontendMemoryUsage(): FrontendMemoryUsage | null {
        return this.frontendMemory;
    }

    /**
     * Formats an uptime value in seconds into a human-readable string.
     */
    private formatUptime(seconds: number): string {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        const parts = [];
        if (hours > 0) parts.push(`${hours}h`);
        if (minutes > 0 || hours > 0) parts.push(`${minutes}m`);
        parts.push(`${secs}s`);

        return parts.join(" ");
    }

    /**
     * Gets comprehensive system information.
     * @returns System information object.
     */
    private getSystemInfo() {
        const BYTES_TO_MB = 1024 * 1024;
        const memoryUsage = process.memoryUsage();
        const uptimeSeconds = process.uptime();

        // Get system memory information
        const totalSystemMemory = os.totalmem();
        const freeSystemMemory = os.freemem();
        const usedSystemMemory = totalSystemMemory - freeSystemMemory;

        // Calculate memory in MB
        const heapUsedMB = (memoryUsage.heapUsed / BYTES_TO_MB).toFixed(2);
        const heapTotalMB = (memoryUsage.heapTotal / BYTES_TO_MB).toFixed(2);
        const rssUsedMB = (memoryUsage.rss / BYTES_TO_MB).toFixed(2);
        const totalSystemMemoryMB = (totalSystemMemory / BYTES_TO_MB).toFixed(2);
        const usedSystemMemoryMB = (usedSystemMemory / BYTES_TO_MB).toFixed(2);

        // Frontend memory (if available)
        let frontendInfo = null;
        const frontendMemory = this.getFrontendMemoryUsage();
        if (frontendMemory) {
            const frontendHeapUsedMB = (frontendMemory.heapUsed / BYTES_TO_MB).toFixed(2);
            const frontendHeapTotalMB = (frontendMemory.heapTotal / BYTES_TO_MB).toFixed(2);
            const frontendRssMB = (frontendMemory.rss / BYTES_TO_MB).toFixed(2);
            const frontendAge = Math.round((Date.now() - frontendMemory.timestamp) / 1000);

            frontendInfo = {
                heapUsed: frontendHeapUsedMB,
                heapTotal: frontendHeapTotalMB,
                rss: frontendRssMB,
                age: frontendAge
            };
        }

        // WebSocket server information
        const webInfo = this.getServerInfo();

        // Command system information
        const allCommands = commandManager.getAllCommands();

        // Plugin system information
        const allPlugins = pluginManager.getAllPlugins();
        const loadedPlugins = allPlugins.filter(p => p.state === "LOADED");

        return {
            app: {
                name: Globals.NAME,
                version: Globals.VERSION,
                uptime: this.formatUptime(uptimeSeconds)
            },
            runtime: {
                platform: `${process.platform} (${process.arch})`,
                nodejs: process.version,
                nuxt: "3.17.5",
                vue: "3.5.16"
            },
            memory: {
                backend: {
                    heapUsed: heapUsedMB,
                    heapTotal: heapTotalMB,
                    rss: rssUsedMB
                },
                frontend: frontendInfo,
                system: {
                    total: totalSystemMemoryMB,
                    used: usedSystemMemoryMB,
                    free: ((totalSystemMemory - usedSystemMemory) / BYTES_TO_MB).toFixed(2)
                },
                total: frontendInfo ? {
                    heapUsed: (parseFloat(heapUsedMB) + parseFloat(frontendInfo.heapUsed)).toFixed(2),
                    heapTotal: (parseFloat(heapTotalMB) + parseFloat(frontendInfo.heapTotal)).toFixed(2),
                    rss: (parseFloat(rssUsedMB) + parseFloat(frontendInfo.rss)).toFixed(2)
                } : null
            },
            websocket: {
                url: `ws://localhost:${webInfo.port}`,
                connectedClients: webInfo.connectedClients,
                historyEntries: webInfo.historyEntries,
                maxHistorySize: webInfo.maxHistorySize,
                rateLimitedClients: webInfo.rateLimitedClients,
                maxCommandsPerMinute: webInfo.maxCommandsPerMinute
            },
            webUI: {
                url: `http://localhost:${Globals.WEB_UI_PORT}`
            },
            commands: {
                total: allCommands.length
            },
            plugins: {
                loaded: loadedPlugins.length,
                total: allPlugins.length
            },
            environment: {
                nodeEnv: process.env.NODE_ENV || "development",
                debugMode: process.env.DEBUG ? "enabled" : "disabled",
                workingDir: process.cwd()
            }
        };
    }

    /**
     * Serializes a service for WebSocket transmission.
     * @param service The service to serialize.
     * @returns A plain object representation of the service.
     */
    private serializeService(service: Service) {
        const connectionStats = service.connectionStats;
        return {
            name: service.metadata.name,
            version: service.metadata.version,
            description: service.metadata.description,
            platform: service.metadata.platform,
            essential: service.metadata.essential,
            requiresAuth: service.metadata.requiresAuth,
            state: service.state,
            autoConnect: service.serviceConfig.autoConnect,
            connectionTimeout: service.serviceConfig.connectionTimeout,
            maxRetries: service.serviceConfig.maxRetries,
            retryDelay: service.serviceConfig.retryDelay,
            connectionStats: {
                state: connectionStats.state,
                attempts: connectionStats.attempts,
                lastConnected: connectionStats.lastConnected?.toISOString(),
                lastAttempt: connectionStats.lastAttempt?.toISOString(),
                isRetrying: connectionStats.isRetrying
            },
            lastError: service.lastError?.message
        };
    }

    /**
     * Gets all services data for WebSocket transmission.
     * @returns Array of serialized services.
     */
    private getServicesData() {
        const services = serviceManager.getAllServices();
        const stats = serviceManager.getStats();

        return {
            services: services.map(service => this.serializeService(service)),
            stats: stats
        };
    }

    /**
     * Stops the WebSocket server and cleans up resources.
     */
    stop() {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        this.clients.clear();
        this.rateLimitMap.clear();
    }

    /**
     * Checks if a client has exceeded the rate limit.
     * @param clientId Unique identifier for the client.
     * @returns True if client should be rate limited.
     */
    private isRateLimited(clientId: string): boolean {
        const now = Date.now();
        const clientLimit = this.rateLimitMap.get(clientId);

        if (!clientLimit || now > clientLimit.resetTime) {
            // Reset or create new rate limit window
            this.rateLimitMap.set(clientId, {
                count: 1,
                resetTime: now + this.rateLimitWindowMs
            });
            return false;
        }

        if (clientLimit.count >= this.maxCommandsPerMinute) {
            return true; // Rate limited
        }

        // Increment count
        clientLimit.count++;
        return false;
    }

    /**
     * Cleans up old rate limit entries to prevent memory leaks.
     */
    private cleanupRateLimitMap() {
        const now = Date.now();
        for (const [clientId, limit] of this.rateLimitMap.entries()) {
            if (now > limit.resetTime) {
                this.rateLimitMap.delete(clientId);
            }
        }
    }

    /**
     * Broadcasts a message to all connected clients.
     * @param message The message to send.
     */
    broadcast(message: WebSocketMessage) {
        // If this is a clear message, clear the history first
        if (message.type === "clear") {
            this.clearHistory();
        }

        const serialized = JSON.stringify(message);
        for (const [client, clientId] of this.clients) {
            if (client.readyState === WebSocket.OPEN) {
                client.send(serialized);
            }
        }
    }

    /**
     * Handles incoming messages from a WebSocket client.
     * @param ws The WebSocket instance.
     * @param message The message received.
     */
    private async handleMessage(ws: WebSocket, message: RawData) {
        try {
            const data: WebSocketMessage = JSON.parse(message.toString());

            // Get the stored client identifier
            const clientId = this.clients.get(ws);
            if (!clientId) {
                logger.error("WebSocket", "Client ID not found for connection");
                return;
            }

            if (data.type === "command") {
                // Security: Rate limiting for commands
                if (this.isRateLimited(clientId)) {
                    logger.warn("WebSocket", `Rate limit exceeded for client ${clientId}`);
                    ws.send(JSON.stringify({
                        type: "error",
                        payload: `Rate limit exceeded. Maximum ${this.maxCommandsPerMinute} commands per minute.`
                    }));
                    return;
                }

                const commandString = data.payload.command;
                //logger.debug(`Received command from WebSocket: ${commandString}`);

                // Add the user command to history
                this.addToHistory("commandResult", "COMMAND", `> ${commandString}`);

                const result = await commandManager.processCommand(commandString);
                this.handleCommandResult(ws, result);
            } else if (data.type === "getCompletions") {
                // Don't rate limit tab completions as heavily, but still track
                const partial = data.payload.partial || "";
                const completions = this.getCommandCompletions(partial);
                ws.send(JSON.stringify({
                    type: "completions",
                    payload: { completions }
                }));
            } else if (data.type === "memoryReport") {
                // Update frontend memory usage
                const memUsage = data.payload.memory || data.payload;
                if (memUsage && typeof memUsage.heapUsed === 'number') {
                    this.frontendMemory = {
                        heapUsed: memUsage.heapUsed,
                        heapTotal: memUsage.heapTotal,
                        rss: memUsage.rss,
                        external: memUsage.external,
                        timestamp: Date.now()
                    };
                }
            } else if (data.type === "systemInfo") {
                // Send system information
                const systemInfo = this.getSystemInfo();
                ws.send(JSON.stringify({
                    type: "systemInfo",
                    payload: systemInfo
                }));
            } else if (data.type === "getServices") {
                // Send service data
                const servicesData = this.getServicesData();
                ws.send(JSON.stringify({
                    type: "serviceData",
                    payload: servicesData
                }));
            }
        } catch (error) {
            logger.error("WebSocket", "Failed to process message", error instanceof Error ? error : new Error(String(error)));
            ws.send(JSON.stringify({ type: "error", payload: "Invalid message format" }));
        }
    }

    /**
     * Sends the result of a command back to the specific client that sent it.
     * @param ws The WebSocket client.
     * @param result The CommandResult.
     */
    private handleCommandResult(ws: WebSocket, result: CommandResult) {
        let message: string | undefined;

        switch (result.status) {
            case CommandStatus.OKAY:
                message = result.message;
                break;
            case CommandStatus.ERROR:
                message = result.message || "An error occurred.";
                if (result.error) {
                    logger.debug("WebSocket", "Error details:", result.error);
                }
                break;
            case CommandStatus.EXIT:
                message = result.message || "Exit command received.";
                // Note: We don't actually exit the app here, as that would kill the server.
                // The frontend can interpret this and provide a "disconnected" message.
                break;
        }

        if (!message) {
            return;
        }

        const statusString = CommandStatus[result.status];

        // Add command result to history
        this.addToHistory("commandResult", statusString, message);

        ws.send(
            JSON.stringify({
                type: "commandResult",
                payload: {
                    status: statusString, // Send status as string for readability
                    message,
                },
            }),
        );
    }

    /**
     * Gets command completions for a partial input string.
     * @param partial The partial command string to complete.
     * @returns Array of completion suggestions.
     */
    private getCommandCompletions(partial: string): CompletionItem[] {
        const trimmedPartial = partial.trim().toLowerCase();
        if (!trimmedPartial) {
            // If empty, return all available main commands with descriptions
            return commandManager.getAllCommands().map(cmd => ({
                name: cmd.name,
                description: cmd.description,
                aliases: cmd.aliases,
                usage: cmd.usage
            })).sort((a, b) => a.name.localeCompare(b.name));
        }

        const parts = trimmedPartial.split(/\s+/);
        if (parts.length === 1) {
            // Single word - first check if it's an exact match for a command with sub-commands
            const exactCommand = commandManager.getCommand(trimmedPartial);
            if (exactCommand && exactCommand.subCommands && exactCommand.subCommands.length > 0) {
                // Show all sub-commands for this exact command
                return exactCommand.subCommands.map(subCmd => ({
                    name: `${exactCommand.name} ${subCmd.name}`,
                    description: subCmd.description,
                    aliases: subCmd.aliases,
                    usage: subCmd.usage,
                    isSubCommand: true,
                    parentCommand: exactCommand.name
                })).sort((a, b) => a.name.localeCompare(b.name));
            }

            // Otherwise, match command names and aliases that start with the partial
            const allCommands = commandManager.getAllCommands();
            const matches: CompletionItem[] = [];

            for (const command of allCommands) {
                const matchesName = command.name.toLowerCase().startsWith(trimmedPartial);
                const matchesAlias = command.aliases?.some(alias =>
                    alias.toLowerCase().startsWith(trimmedPartial)
                );

                if (matchesName || matchesAlias) {
                    matches.push({
                        name: command.name,
                        description: command.description,
                        aliases: command.aliases,
                        usage: command.usage
                    });
                }
            }

            return matches.sort((a, b) => a.name.localeCompare(b.name));
        } else {
            // Multiple words - check for sub-commands
            const [commandName, ...subArgs] = parts;
            if (!commandName) return [];

            const command = commandManager.getCommand(commandName);

            if (command && command.subCommands) {
                if (subArgs.length === 1 && subArgs[0]) {
                    // Completing a sub-command
                    const subPartial = subArgs[0];
                    const matches: CompletionItem[] = [];

                    for (const subCommand of command.subCommands) {
                        const matchesName = subCommand.name.toLowerCase().startsWith(subPartial);
                        const matchesAlias = subCommand.aliases?.some(alias =>
                            alias.toLowerCase().startsWith(subPartial)
                        );

                        if (matchesName || matchesAlias) {
                            matches.push({
                                name: `${commandName} ${subCommand.name}`,
                                description: subCommand.description,
                                aliases: subCommand.aliases,
                                usage: subCommand.usage,
                                isSubCommand: true,
                                parentCommand: commandName
                            });
                        }
                    }

                    return matches.sort((a, b) => a.name.localeCompare(b.name));
                } else if (subArgs.length === 0 || (subArgs.length === 1 && !subArgs[0])) {
                    // Show all sub-commands for this command
                    return command.subCommands.map(subCmd => ({
                        name: `${commandName} ${subCmd.name}`,
                        description: subCmd.description,
                        aliases: subCmd.aliases,
                        usage: subCmd.usage,
                        isSubCommand: true,
                        parentCommand: commandName
                    })).sort((a, b) => a.name.localeCompare(b.name));
                } else {
                    // Check for nested sub-commands
                    const [subCommandName, ...nestedArgs] = subArgs;
                    if (!subCommandName) return [];

                    const subCommand = command.subCommands.find(sc =>
                        sc.name.toLowerCase() === subCommandName.toLowerCase() ||
                        (sc.aliases && sc.aliases.some(alias => alias.toLowerCase() === subCommandName.toLowerCase()))
                    );

                    if (subCommand && subCommand.subCommands && nestedArgs.length === 1 && nestedArgs[0]) {
                        const nestedPartial = nestedArgs[0];
                        const matches: CompletionItem[] = [];

                        for (const nestedSubCommand of subCommand.subCommands) {
                            const matchesName = nestedSubCommand.name.toLowerCase().startsWith(nestedPartial);
                            const matchesAlias = nestedSubCommand.aliases?.some(alias =>
                                alias.toLowerCase().startsWith(nestedPartial)
                            );

                            if (matchesName || matchesAlias) {
                                matches.push({
                                    name: `${commandName} ${subCommandName} ${nestedSubCommand.name}`,
                                    description: nestedSubCommand.description,
                                    aliases: nestedSubCommand.aliases,
                                    usage: nestedSubCommand.usage,
                                    isSubCommand: true,
                                    parentCommand: `${commandName} ${subCommandName}`
                                });
                            }
                        }

                        return matches.sort((a, b) => a.name.localeCompare(b.name));
                    }
                }
            }
        }

        return [];
    }
}

export const webSocketManager = new WebSocketManager();
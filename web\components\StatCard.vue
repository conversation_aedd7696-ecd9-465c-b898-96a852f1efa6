<template>
    <div class="stat-card glass">
        <div class="stat-main">
            <div class="stat-icon">
                <slot name="icon"></slot>
            </div>
            <div class="stat-content">
                <div class="stat-header">
                    <h3>{{ title }}</h3>
                    <div class="stat-values">
                        <p class="stat-value">{{ value }}</p>
                        <p v-if="subtitle" class="stat-subtitle">{{ subtitle }}</p>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="progressPercentage !== undefined" class="progress-section">
            <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            </div>
            <span class="progress-text">{{ progressPercentage }}%</span>
        </div>
        <div v-else class="divider-section">
            <div class="divider-line"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Props {
    title: string
    value: string | number
    subtitle?: string
    progressPercentage?: number
}

defineProps<Props>()
defineSlots<{
    icon(): any
}>()
</script>

<style scoped>
.stat-card {
    padding: var(--space-md);
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    transition: var(--transition);
    height: 100px;
    min-width: 0;
}

.stat-card:hover {
    transform: translateY(-2px);
    border-color: var(--border-hover);
}

.stat-main {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.stat-icon {
    width: 32px;
    height: 32px;
    background: var(--bg-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon :deep(svg),
.stat-icon :deep(.icon) {
    color: var(--color-success);
    width: 18px;
    height: 18px;
}

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--space-sm);
}

.stat-header h3 {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 1;
}

.stat-values {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex-shrink: 0;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
    white-space: nowrap;
    font-family: var(--font-mono);
    line-height: 1.2;
}

.stat-subtitle {
    font-size: 11px;
    font-weight: 400;
    margin: 0;
    color: var(--text-secondary);
    opacity: 0.7;
    white-space: nowrap;
    font-family: var(--font-mono);
    line-height: 1.2;
    margin-top: 2px;
}

.progress-section {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-top: var(--space-md);
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.progress-fill {
    height: 100%;
    background: rgba(76, 175, 80, 0.95);
    box-shadow: 0 0 6px rgba(76, 175, 80, 0.3);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 28px;
    text-align: right;
    font-family: var(--font-mono);
}

.divider-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--space-md);
    padding: var(--space-xs) 0;
}

.divider-line {
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 30%, var(--border-color) 70%, transparent 100%);
    opacity: 0.6;
}
</style>

<template>
    <div class="app-layout">
        <!-- Floating Background Orbs -->
        <div class="bg-orb"></div>
        <div class="bg-orb"></div>
        <div class="bg-orb"></div>

        <!-- Additional Background Elements -->
        <div class="bg-mesh"></div>
        <div class="bg-squares"></div>

        <header class="app-header glass">
            <div class="header-content">
                <div class="logo">
                    <Icon name="lucide:layers" size="24" class="logo-icon" />
                    <span>ChatBot Control Panel</span>
                </div>
                <div class="header-actions">
                    <div class="connection-status">
                        <span :class="['status-indicator', connectionStatus]"></span>
                        <span class="status-text">{{ connectionStatusText }}</span>
                    </div>
                    <div class="uptime-status">
                        <Icon name="lucide:clock" size="16" class="uptime-icon" />
                        <span class="uptime-text">{{ uptime ? formatUptime(uptime) : 'Loading...' }}</span>
                    </div>
                </div>
            </div>
            <nav class="tabs">
                <NuxtLink v-for="tab in tabs" :key="tab.path" :to="tab.path" class="tab" :class="{ active: isActiveTab(tab.path) }">
                    <Icon :name="tab.icon" size="16" class="tab-icon" />
                    {{ tab.name }}
                </NuxtLink>
            </nav>
        </header>

        <main class="app-main">
            <slot />
        </main>
    </div>
</template>

<script setup lang="ts">
import { onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// Use shared WebSocket connection
const { connectionStatus, connectionStatusText, uptime, formatUptime, disconnect } = useWebSocket()

onUnmounted(() => {
    // Clean up WebSocket connection when layout unmounts
    disconnect();
});

// Navigation tabs
const tabs = [
    {
        name: 'Overview',
        path: '/',
        icon: 'lucide:layout-grid'
    },
    {
        name: 'Services',
        path: '/services',
        icon: 'lucide:compass'
    },
    {
        name: 'Terminal',
        path: '/terminal',
        icon: 'lucide:terminal'
    },
    {
        name: 'Logs',
        path: '/logs',
        icon: 'lucide:file-text'
    },
    {
        name: 'Settings',
        path: '/settings',
        icon: 'lucide:settings'
    }
]

const isActiveTab = (path: string) => {
    return route.path === path || (path === '/' && route.path === '/index')
}
</script>

<style scoped>
.app-layout {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: transparent;
}

.app-header {
    position: sticky;
    top: 0;
    z-index: 100;
    border-radius: 0;
}

.app-header.glass {
    border: none !important;
    border-top: none !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08),
        inset 0 -1px 0 rgba(255, 255, 255, 0.05) !important;
}

.app-header .tabs {
    border-bottom: 1px solid var(--border-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 700;
    font-size: 18px;
    color: var(--text-primary);
}

.logo :deep(svg),
.logo .icon,
.logo-icon {
    color: var(--color-success);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 13px;
    color: var(--text-secondary);
}

.status-indicator.online {
    animation: pulse 2s infinite;
}

.status-text {
    font-weight: 500;
}

.uptime-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 13px;
    color: var(--text-secondary);
    padding-right: var(--space-md);
}

.uptime-status :deep(svg),
.uptime-status .icon,
.uptime-icon {
    color: var(--color-info);
}

.uptime-text {
    font-weight: 500;
    font-family: var(--font-mono);
}

.tabs {
    overflow-x: auto;
}

.tab {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
}

.tab-icon {
    width: 16px;
    height: 16px;
}

.app-main {
    flex: 1;
    overflow: hidden;
    padding: var(--space-lg);
}

/* Responsive tab text hiding */
@media (max-width: 700px) {
    .tab {
        justify-content: center;
        min-width: 48px;
    }

    /* Hide text content on small screens, keep only icons */
    .tab:not(.tab-icon) {
        font-size: 0;
    }

    .tab-icon {
        width: 20px;
        height: 20px;
    }
}

@media (max-width: 450px) {
    .tab {
        min-width: 44px;
        padding: var(--space-md) var(--space-sm);
    }

    .tab-icon {
        width: 18px;
        height: 18px;
    }
}
</style>
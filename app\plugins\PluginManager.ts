import { fileURLToPath, pathToFileURL } from "node:url";
import { dirname } from "node:path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

import { readdir } from "node:fs/promises";
import * as path from "node:path";
import { commandManager } from "../commands/CommandManager";
import { CoreEvents } from "../events/CoreEvents";
import { eventManager } from "../events/EventManager";
import { logger } from "../../shared";
import { Plugin, PluginState, PluginResult } from "./Plugin";

/**
 * Central manager for the plugin system.
 * Handles loading, unloading, reloading plugins, dependency resolution,
 * and integration with the CommandManager.
 */
export class PluginManager {
    /** Map of loaded plugins by name */
    private plugins: Map<string, Plugin> = new Map();

    /** Set of plugin names that are currently being loaded to prevent circular dependencies */
    private loading: Set<string> = new Set();

    /**
     * Loads a plugin instance and registers its commands with the CommandManager.
     * @param plugin The plugin instance to load.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async loadPlugin(plugin: Plugin): Promise<PluginResult> {
        const pluginName = plugin.metadata.name;

        // Check if plugin is already loaded
        if (this.plugins.has(pluginName)) {
            return PluginResult.error(`Plugin '${pluginName}' is already loaded`);
        }

        // Check for circular loading
        if (this.loading.has(pluginName)) {
            return PluginResult.error(`Plugin '${pluginName}' has a circular dependency`);
        }

        try {
            this.loading.add(pluginName);
            logger.info(pluginName, `Loading v${plugin.metadata.version}`);

            await eventManager.emit(CoreEvents.pluginLoading, plugin);
            const result = await plugin.load(this.plugins);

            if (result.success) {
                // Register plugin
                this.plugins.set(pluginName, plugin);

                // Register plugin's commands with CommandManager
                this.registerPluginCommands(plugin);
                logger.debug(pluginName, `Registered ${plugin.commands.length} commands`);

                // Emit event after successful load and command registration
                await eventManager.emit(CoreEvents.pluginLoaded, plugin);
            } else {
                const error = result.error || new Error(result.message || "Unknown plugin load error");
                await eventManager.emit(CoreEvents.pluginError, { plugin, error });
                logger.error(pluginName, `Failed to load: ${result.message}`, result.error);
            }

            this.loading.delete(pluginName);
            return result;
        } catch (error) {
            this.loading.delete(pluginName);
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Plugin '${pluginName}' failed to load`;
            await eventManager.emit(CoreEvents.pluginError, { plugin, error: err });
            logger.error(pluginName, message, err);
            return PluginResult.error(message, err);
        }
    }

    /**
     * Unloads a plugin and removes its commands from the CommandManager.
     * @param pluginName The name of the plugin to unload.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async unloadPlugin(pluginName: string): Promise<PluginResult> {
        const plugin = this.plugins.get(pluginName);
        if (!plugin) {
            return PluginResult.error(`Plugin '${pluginName}' is not loaded`);
        }

        // Check if plugin is locked
        if (plugin.metadata.locked) {
            return PluginResult.error(`Plugin '${pluginName}' is locked and cannot be unloaded`);
        }

        try {
            logger.debug(pluginName, `Being unloaded...`);

            await eventManager.emit(CoreEvents.pluginUnloading, plugin);
            const commandCount = plugin.commands.length;
            // Before actual unloading, unregister its commands
            this.unregisterPluginCommands(plugin);
            logger.debug(pluginName, `Removed ${commandCount} commands`);

            // Unload the plugin
            const result = await plugin.unload();

            if (result.success) {
                this.plugins.delete(pluginName);
                // Emit event after successful unload
                await eventManager.emit(CoreEvents.pluginUnloaded, plugin);
                logger.info(pluginName, `Unloaded successfully`);
            } else {
                const error = result.error || new Error(result.message || "Unknown plugin unload error");
                await eventManager.emit(CoreEvents.pluginError, { plugin, error });
                logger.error(pluginName, `Failed to unload: ${result.message}`, result.error);
                // Even if unload fails, we should consider it removed from the active list.
                this.plugins.delete(pluginName);
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Plugin '${pluginName}' failed to unload`;
            await eventManager.emit(CoreEvents.pluginError, { plugin, error: err });
            logger.error(pluginName, message, err);
            return PluginResult.error(message, err);
        }
    }

    /**
     * Reloads a plugin.
     * @param pluginName The name of the plugin to reload.
     * @returns Promise resolving to a PluginResult indicating success or failure.
     */
    async reloadPlugin(pluginName: string): Promise<PluginResult> {
        const plugin = this.plugins.get(pluginName);
        if (!plugin) {
            return PluginResult.error(`Plugin '${pluginName}' is not loaded`);
        }

        // Check if plugin is locked
        if (plugin.metadata.locked) {
            return PluginResult.error(`Plugin '${pluginName}' is locked and cannot be reloaded`);
        }

        try {
            logger.debug(pluginName, `Being reloaded...`);

            await eventManager.emit(CoreEvents.pluginLoading, plugin); // Treat reload as a loading event for listeners
            const reloadResult = await plugin.reload(this.plugins);

            if (reloadResult.success) {
                const commandCount = plugin.commands.length;
                this.unregisterPluginCommands(plugin); // Unregister old commands first
                this.registerPluginCommands(plugin); // Register new/updated commands
                const newCommandCount = plugin.commands.length;
                logger.debug(pluginName, `${newCommandCount - commandCount} commands were added/removed`);
                await eventManager.emit(CoreEvents.pluginReloaded, plugin);
                logger.info(pluginName, `Reloaded successfully`);
            } else {
                const error = reloadResult.error || new Error(reloadResult.message || "Unknown plugin reload error");
                await eventManager.emit(CoreEvents.pluginError, { plugin, error });
            }
            return reloadResult;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            await eventManager.emit(CoreEvents.pluginError, { plugin, error: err });
            logger.error(pluginName, `An unexpected error occurred while reloading`, err);
            return PluginResult.error(`Unexpected error during reload: ${err.message}`, err);
        }
    }

    /**
     * Discovers and loads plugin files from a specified directory.
     * @param relativeDirToLoadFrom The directory path, relative to this file, from which to load plugins.
     * @param requireLocked Whether plugins in this directory should be marked as locked.
     * @returns Promise resolving to an array of results for each plugin load attempt.
     */
    async loadPluginsFromDir(relativeDirToLoadFrom: string, requireLocked: boolean = false): Promise<PluginResult[]> {
        const pluginDir = path.resolve(__dirname, relativeDirToLoadFrom);
        logger.debug("PluginManager", `Scanning for plugins in: ${pluginDir}`);

        const results: PluginResult[] = [];

        try {
            const files = await readdir(pluginDir);

            for (const file of files) {
                // Look for TypeScript plugin files
                if (file.endsWith(".ts") && !file.endsWith(".d.ts")) {
                    const filePath = path.join(pluginDir, file);
                    try {
                        const module = await import(pathToFileURL(filePath).href);
                        const PluginClass = module.default as (new () => Plugin) | undefined;

                        // Check if the default export is a valid plugin class
                        if (PluginClass && typeof PluginClass === "function" && PluginClass.prototype instanceof Plugin) {
                            const pluginInstance = new PluginClass();

                            // Override locked status if required by directory
                            if (requireLocked && !pluginInstance.metadata.locked) {
                                // We need to modify the metadata, but it's readonly.
                                // We'll handle this in the plugin implementation by convention.
                                logger.debug("PluginManager", `${pluginInstance.metadata.name} from ${relativeDirToLoadFrom} is being treated as locked`);
                            }

                            const result = await this.loadPlugin(pluginInstance);
                            results.push(result);

                            if (!result.success) {
                                logger.error("PluginManager", `${pluginInstance.metadata.name} failed to load from ${file}: ${result.message}`);
                            }
                        } else {
                            const errorMsg = `File ${filePath} does not have a valid default export of a Plugin class`;
                            logger.warn("PluginManager", errorMsg);
                            results.push(PluginResult.error(errorMsg));
                        }
                    } catch (e: any) {
                        const errorMsg = `Error importing or instantiating plugin from ${filePath}`;
                        logger.error(errorMsg, e);
                        results.push(PluginResult.error(errorMsg, e));
                    }
                }
            }
        } catch (e: any) {
            const errorMsg = `Error reading plugins directory ${pluginDir}`;
            logger.error(errorMsg, e);
            results.push(PluginResult.error(errorMsg, e));
        }

        return results;
    }

    /**
     * Gets a loaded plugin by name.
     * @param pluginName The name of the plugin to retrieve.
     * @returns The plugin instance, or undefined if not found.
     */
    getPlugin(pluginName: string): Plugin | undefined {
        return this.plugins.get(pluginName);
    }

    /**
     * Gets all loaded plugins.
     * @returns Array of all loaded plugin instances.
     */
    getAllPlugins(): Plugin[] {
        return Array.from(this.plugins.values());
    }

    /**
     * Gets plugin names with their current states.
     * @returns Map of plugin names to their current states.
     */
    getPluginStates(): Map<string, PluginState> {
        const states = new Map<string, PluginState>();
        for (const [name, plugin] of this.plugins) {
            states.set(name, plugin.state);
        }
        return states;
    }

    /**
     * Checks if a plugin is loaded.
     * @param pluginName The name of the plugin to check.
     * @returns True if the plugin is loaded, false otherwise.
     */
    isPluginLoaded(pluginName: string): boolean {
        const plugin = this.plugins.get(pluginName);
        return plugin?.state === PluginState.LOADED;
    }

    /**
     * Gets statistics about the plugin system.
     * @returns Object containing plugin system statistics.
     */
    getStats() {
        const totalPlugins = this.plugins.size;
        const loadedPlugins = Array.from(this.plugins.values()).filter((p) => p.state === PluginState.LOADED).length;
        const lockedPlugins = Array.from(this.plugins.values()).filter((p) => p.metadata.locked).length;
        const totalCommands = Array.from(this.plugins.values()).reduce((sum, p) => sum + p.commands.length, 0);
        return { totalPlugins, loadedPlugins, lockedPlugins, totalCommands, errorPlugins: totalPlugins - loadedPlugins };
    }

    /**
     * Initializes the plugin system by loading core plugins.
     * This method should be called during application startup.
     */
    async initialize() {
        logger.info("PluginManager", "Initializing...");

        // Clear any existing plugins
        this.plugins.clear();
        this.loading.clear();

        // Load default plugins first (these will be locked)
        logger.info("PluginManager", "Loading default plugins...");
        const defaultResults = await this.loadPluginsFromDir("./defaults", true);

        const allResults = [...defaultResults];
        const successful = allResults.filter((r) => r.success).length;
        const failed = allResults.filter((r) => !r.success).length;

        logger.info("PluginManager", `Initialized: ${successful} plugins loaded successfully, ${failed} failed`);

        if (failed > 0) {
            logger.warn("PluginManager", "Some plugins failed to load. Check the logs above for details.");
        }
    }

    /**
     * Shuts down the plugin system by unloading all non-locked plugins.
     */
    async shutdown() {
        logger.debug("PluginManager", "Shutting down...");

        const pluginsToUnload = Array.from(this.plugins.keys()).filter((name) => {
            const plugin = this.plugins.get(name);
            return plugin && !plugin.metadata.locked;
        });

        for (const pluginName of pluginsToUnload) {
            await this.unloadPlugin(pluginName);
        }

        logger.debug("PluginManager", "Shutdown complete");
    }

    /**
     * Clears all plugins from the manager.
     * WARNING: This method is intended for testing purposes only.
     */
    public clearAll() {
        this.plugins.clear();
        this.loading.clear();
    }

    private unregisterPluginCommands(plugin: Plugin) {
        for (const command of plugin.commands) {
            commandManager.removeCommand(command.name);
        }
    }

    private registerPluginCommands(plugin: Plugin) {
        for (const command of plugin.commands) {
            commandManager.addCommand(command);
        }
    }
}

export const pluginManager = new PluginManager();

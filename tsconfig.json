{
    "compilerOptions": {
        // Environment setup & latest features
        "lib": ["ESNext"],
        "target": "ESNext",
        "module": "ESNext",
        "moduleDetection": "force",
        "jsx": "react-jsx",
        "allowJs": true,
        "rootDir": ".",
        "forceConsistentCasingInFileNames": true,

        // Bundler mode
        "moduleResolution": "bundler",
        "allowImportingTsExtensions": true,
        "verbatimModuleSyntax": true,
        "noEmit": true,

        // Best practices
        "strict": true,
        "skipLibCheck": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedIndexedAccess": true,
        "noImplicitOverride": true,

        // Some stricter flags (disabled by default)
        "noUnusedLocals": false,
        "noUnusedParameters": false,
        "noPropertyAccessFromIndexSignature": false
    },
    "include": ["app/**/*", "shared/**/*"],
    "exclude": ["node_modules", "app/node_modules", "app/dist", "web/**/*"]
}

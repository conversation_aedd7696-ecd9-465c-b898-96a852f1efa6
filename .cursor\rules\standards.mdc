# ChatBotTS Standards

## Code Style
- **Formatting**
  - Indentation: 4 spaces, no tabs
  - New lines between logical blocks for readability
  - No trailing whitespace
  - Maximum line length: 120 characters
  - No emojis in code (comments, strings, etc.)
- **Return Types**
  - Avoid explicitly typing function return values as `void` or `Promise<void>`.
  - If a function truly returns nothing, allow TypeScript to infer `void`.
  - For async functions that don't return a value, allow inference or consider if a more meaningful status/result could be returned (e.g., `Promise<boolean>` for success/failure).

- **Visibility Modifiers**
  - Omit the `public` keyword for class members (methods, properties) as it's the default in TypeScript.
  - Explicitly use `private` or `protected` when a member should not be publicly accessible according to its intended design.

- **Documentation**
  - JSDoc for all public APIs (classes, methods, functions)
  - Clearly describe purpose, parameters, return values, and any exceptions thrown
  - Example:
    ```typescript
    /**
     * Processes a given command string.
     * @param commandString The raw command string to process.
     * @throws Error if the command is invalid.
     */
    async processCommand(commandString: string) { /* ... */ }
    ```

## Development Environment & Practices
- **Node.js Runtime**
  - Use Node.js built-in modules and standard APIs
  - Prefer performance.now() for high-resolution timing over Date.now()
  - Use standard Node.js patterns for server creation and WebSocket handling
  - Leverage npm/tsx for development and build processes

- **Logging** (using `app/logging/logger.ts`)
  - **Levels (in order of severity)**:
    - `logger.error()`: For critical errors that disrupt functionality
    - `logger.warn()`: For potential issues or unexpected behavior
    - `logger.info()`: For general operational messages and status updates
    - `logger.debug()`: For detailed debugging information (visible only when `NODE_ENV=development` or `DEBUG=true`)
  - **Helper Methods**:
    - `logger.newLine()`: For adding visual spacing
    - `logger.prompt()`: For user input prompts
    - `logger.section()`: For creating clear section titles in output
    - `logger.keyValue()`: For displaying key-value pairs neatly

## Command Structure (`app/commands/defaults/`)
- Each command must reside in its own file (e.g., `HelpCommand.ts`)
- Implement the `Command` interface
- Provide a `static commandInfo` object detailing the command's name, description, and usage
- Utilize the `@command` decorator for registration with the `CommandManager`
- Ensure commands are single-purpose and focused
- Perform thorough input validation and provide clear, user-friendly error messages



- Ensure commands are single-purpose and focused
- Perform thorough input validation and provide clear, user-friendly error messages



// Load environment variables first using dotenv-mono
import { dotenvLoad } from "dotenv-mono";

// Initialize dotenv-mono for monorepo environment management
dotenvLoad({
    depth: 4,
    priorities: {
        ".env.defaults": 1,
        ".env": 25,
        ".env.local": 50,
        ".env.development": 75,
        ".env.production": 75,
    },
});

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    compatibilityDate: "2025-05-15",
    devtools: { enabled: true },
    pages: true,
    ssr: true, // Enable SSR for proper initial page loading
    css: ["~/assets/css/main.css"],
    icon: {
        class: "icon",
        mode: "css",
        size: "1em",
    },

    runtimeConfig: {
        public: {
            webUiPort: parseInt(process.env.WEB_UI_PORT || "3000"), // Default to 3000 if not set
        },
    },

    app: {
        head: {
            title: "ChatBot Control Panel",
            meta: [{ charset: "utf-8" }, { name: "viewport", content: "width=device-width, initial-scale=1" }],
            link: [
                {
                    rel: "stylesheet",
                    href: "https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&family=Inter:wght@400;500;600;700&display=swap",
                },
            ],
        },
    },

    // Router configuration to preserve page state
    router: {
        options: {
            hashMode: false,
        },
    },

    // Ensure proper handling of client-side navigation
    experimental: {
        payloadExtraction: false, // Helps with client-side navigation
    },

    // Nitro configuration for proper SPA handling
    nitro: {
        routeRules: {
            "/**": { prerender: false },
        },
    },

    modules: ["@nuxt/icon"],
});

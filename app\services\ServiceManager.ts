import { fileURLToPath, pathToFileURL } from "node:url";
import { dirname } from "node:path";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

import { readdir } from "node:fs/promises";
import * as path from "node:path";
import { CoreEvents } from "../events/CoreEvents";
import { eventManager } from "../events/EventManager";
import { logger } from "../../shared";
import { Service, ServiceState, ServiceResult } from "./Service";

/**
 * Central manager for the service system.
 * Handles registration, connection lifecycle, health monitoring,
 * and integration with external platforms.
 */
export class ServiceManager {
    /** Map of registered services by name */
    private services: Map<string, Service> = new Map();

    /** Set of service names that are currently connecting to prevent race conditions */
    private connecting: Set<string> = new Set();

    /** Health check interval timer */
    private healthCheckTimer?: NodeJS.Timeout;

    /** Health check interval in milliseconds */
    private readonly healthCheckInterval = 60000; // 1 minute

    /**
     * Registers a service instance.
     * @param service The service instance to register.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async registerService(service: Service): Promise<ServiceResult> {
        const serviceName = service.metadata.name;

        // Check if service is already registered
        if (this.services.has(serviceName)) {
            return ServiceResult.error(`Service '${serviceName}' is already registered`);
        }

        try {
            logger.info(serviceName, `Registering v${service.metadata.version}`);

            // Register service
            this.services.set(serviceName, service);

            logger.info(serviceName, `Registered successfully`);
            return ServiceResult.success(`Service '${serviceName}' registered successfully`);
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' failed to register`;
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Unregisters and disconnects a service.
     * @param serviceName The name of the service to unregister.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async unregisterService(serviceName: string): Promise<ServiceResult> {
        const service = this.services.get(serviceName);
        if (!service) {
            return ServiceResult.error(`Service '${serviceName}' is not registered`);
        }

        try {
            logger.debug(serviceName, `Unregistering...`);

            // Disconnect if connected
            if (service.state === ServiceState.CONNECTED || service.state === ServiceState.CONNECTING) {
                const disconnectResult = await this.disconnectService(serviceName);
                if (!disconnectResult.success) {
                    logger.warn(serviceName, `Failed to disconnect cleanly during unregistration`);
                }
            }

            // Cleanup service resources
            await service.cleanup();

            // Remove from registry
            this.services.delete(serviceName);

            logger.info(serviceName, `Unregistered successfully`);
            return ServiceResult.success(`Service '${serviceName}' unregistered successfully`);
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' failed to unregister`;
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Connects a specific service.
     * @param serviceName The name of the service to connect.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async connectService(serviceName: string): Promise<ServiceResult> {
        const service = this.services.get(serviceName);
        if (!service) {
            return ServiceResult.error(`Service '${serviceName}' is not registered`);
        }

        // Check for concurrent connection attempts
        if (this.connecting.has(serviceName)) {
            return ServiceResult.error(`Service '${serviceName}' is already connecting`);
        }

        try {
            this.connecting.add(serviceName);

            await eventManager.emit(CoreEvents.serviceConnecting, service);
            const result = await service.connect();

            if (result.success) {
                await eventManager.emit(CoreEvents.serviceConnected, service);
                logger.info(serviceName, `Connected successfully`);
            } else {
                await eventManager.emit(CoreEvents.serviceError, { service, error: result.error || new Error(result.message || "Connection failed") });
            }

            this.connecting.delete(serviceName);
            return result;
        } catch (error) {
            this.connecting.delete(serviceName);
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' connection failed`;
            await eventManager.emit(CoreEvents.serviceError, { service, error: err });
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Disconnects a specific service.
     * @param serviceName The name of the service to disconnect.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async disconnectService(serviceName: string): Promise<ServiceResult> {
        const service = this.services.get(serviceName);
        if (!service) {
            return ServiceResult.error(`Service '${serviceName}' is not registered`);
        }

        try {
            await eventManager.emit(CoreEvents.serviceDisconnecting, service);
            const result = await service.disconnect();

            if (result.success) {
                await eventManager.emit(CoreEvents.serviceDisconnected, service);
                logger.info(serviceName, `Disconnected successfully`);
            } else {
                await eventManager.emit(CoreEvents.serviceError, { service, error: result.error || new Error(result.message || "Disconnection failed") });
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' disconnection failed`;
            await eventManager.emit(CoreEvents.serviceError, { service, error: err });
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Reconnects a specific service.
     * @param serviceName The name of the service to reconnect.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async reconnectService(serviceName: string): Promise<ServiceResult> {
        const service = this.services.get(serviceName);
        if (!service) {
            return ServiceResult.error(`Service '${serviceName}' is not registered`);
        }

        try {
            logger.info(serviceName, `Reconnecting...`);

            await eventManager.emit(CoreEvents.serviceReconnecting, service);
            const result = await service.reconnect();

            if (result.success) {
                await eventManager.emit(CoreEvents.serviceConnected, service);
                logger.info(serviceName, `Reconnected successfully`);
            } else {
                await eventManager.emit(CoreEvents.serviceError, { service, error: result.error || new Error(result.message || "Reconnection failed") });
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' reconnection failed`;
            await eventManager.emit(CoreEvents.serviceError, { service, error: err });
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Sends a message through a specific service.
     * @param serviceName The name of the service to send through.
     * @param message The message to send.
     * @returns Promise resolving to a ServiceResult indicating success or failure.
     */
    async sendMessage(serviceName: string, message: any): Promise<ServiceResult> {
        const service = this.services.get(serviceName);
        if (!service) {
            return ServiceResult.error(`Service '${serviceName}' is not registered`);
        }

        try {
            const result = await service.sendMessage(message);

            if (result.success) {
                await eventManager.emit(CoreEvents.serviceMessageSent, { service, message, result });
            } else {
                await eventManager.emit(CoreEvents.serviceError, { service, error: result.error || new Error(result.message || "Message send failed") });
            }

            return result;
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            const message = `Service '${serviceName}' failed to send message`;
            await eventManager.emit(CoreEvents.serviceError, { service, error: err });
            logger.error(serviceName, message, err);
            return ServiceResult.error(message, err);
        }
    }

    /**
     * Discovers and loads service files from a specified directory.
     * @param relativeDirToLoadFrom The directory path, relative to this file, from which to load services.
     * @returns Promise resolving to an array of results for each service registration attempt.
     */
    async loadServicesFromDir(relativeDirToLoadFrom: string): Promise<ServiceResult[]> {
        const serviceDir = path.resolve(__dirname, relativeDirToLoadFrom);
        logger.debug("ServiceManager", `Scanning for services in: ${serviceDir}`);

        const results: ServiceResult[] = [];

        try {
            const files = await readdir(serviceDir);

            for (const file of files) {
                // Look for TypeScript service files
                if (file.endsWith(".ts") && !file.endsWith(".d.ts")) {
                    const filePath = path.join(serviceDir, file);
                    try {
                        const module = await import(pathToFileURL(filePath).href);
                        const ServiceClass = module.default as (new () => Service) | undefined;

                        // Check if the default export is a valid service class
                        if (ServiceClass && typeof ServiceClass === "function" && ServiceClass.prototype instanceof Service) {
                            const serviceInstance = new ServiceClass();
                            const result = await this.registerService(serviceInstance);
                            results.push(result);

                            if (!result.success) {
                                logger.error(serviceInstance.metadata.name, `Failed to register from ${file}: ${result.message}`);
                            }
                        } else {
                            const errorMsg = `File ${filePath} does not have a valid default export of a Service class`;
                            logger.warn("ServiceManager", errorMsg);
                            results.push(ServiceResult.error(errorMsg));
                        }
                    } catch (e: any) {
                        const errorMsg = `Error importing or instantiating service from ${filePath}`;
                        logger.error(errorMsg, e);
                        results.push(ServiceResult.error(errorMsg, e));
                    }
                }
            }
        } catch (e: any) {
            const errorMsg = `Error reading services directory ${serviceDir}`;
            logger.error(errorMsg, e);
            results.push(ServiceResult.error(errorMsg, e));
        }

        return results;
    }

    /**
     * Connects all services that have autoConnect enabled.
     * @returns Promise resolving to an array of connection results.
     */
    async connectAutoServices(): Promise<ServiceResult[]> {
        const autoServices = Array.from(this.services.values())
            .filter(service => service.serviceConfig.autoConnect);

        if (autoServices.length === 0) {
            logger.debug("ServiceManager", "No services configured for auto-connect");
            return [];
        }

        logger.info("ServiceManager", `Auto-connecting ${autoServices.length} services`);
        const results: ServiceResult[] = [];

        for (const service of autoServices) {
            try {
                const result = await this.connectService(service.metadata.name);
                results.push(result);

                if (!result.success) {
                    logger.warn(service.metadata.name, `Auto-connect failed: ${result.message}`);
                }
            } catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));
                const result = ServiceResult.error(`Auto-connect failed for '${service.metadata.name}'`, err);
                results.push(result);
                logger.error(service.metadata.name, `Auto-connect error`, err);
            }
        }

        const successful = results.filter(r => r.success).length;
        logger.info("ServiceManager", `Auto-connect completed: ${successful}/${results.length} services connected`);

        return results;
    }

    /**
     * Performs health checks on all connected services.
     * @returns Promise resolving to a map of service names to health check results.
     */
    async performHealthChecks(): Promise<Map<string, ServiceResult>> {
        const results = new Map<string, ServiceResult>();
        const connectedServices = Array.from(this.services.values())
            .filter(service => service.state === ServiceState.CONNECTED);

        if (connectedServices.length === 0) {
            return results;
        }

        logger.debug("ServiceManager", `Performing health checks on ${connectedServices.length} connected services`);

        for (const service of connectedServices) {
            try {
                const result = await service.healthCheck();
                results.set(service.metadata.name, result);

                if (!result.success) {
                    logger.warn(service.metadata.name, `Health check failed: ${result.message}`);
                    await eventManager.emit(CoreEvents.serviceError, {
                        service,
                        error: result.error || new Error(result.message || "Health check failed")
                    });
                }
            } catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));
                const result = ServiceResult.error("Health check failed", err);
                results.set(service.metadata.name, result);
                logger.error(service.metadata.name, `Health check error`, err);
            }
        }

        return results;
    }

    /**
     * Gets a registered service by name.
     * @param serviceName The name of the service to retrieve.
     * @returns The service instance, or undefined if not found.
     */
    getService(serviceName: string): Service | undefined {
        return this.services.get(serviceName);
    }

    /**
     * Gets all registered services.
     * @returns Array of all registered service instances.
     */
    getAllServices(): Service[] {
        return Array.from(this.services.values());
    }

    /**
     * Gets service names with their current states.
     * @returns Map of service names to their current states.
     */
    getServiceStates(): Map<string, ServiceState> {
        const states = new Map<string, ServiceState>();
        for (const [name, service] of this.services) {
            states.set(name, service.state);
        }
        return states;
    }

    /**
     * Checks if a service is registered and connected.
     * @param serviceName The name of the service to check.
     * @returns True if the service is registered and connected, false otherwise.
     */
    isServiceConnected(serviceName: string): boolean {
        const service = this.services.get(serviceName);
        return service?.state === ServiceState.CONNECTED;
    }

    /**
     * Gets statistics about the service system.
     * @returns Object containing service system statistics.
     */
    getStats() {
        const totalServices = this.services.size;
        const connectedServices = Array.from(this.services.values()).filter(s => s.state === ServiceState.CONNECTED).length;
        const connectingServices = Array.from(this.services.values()).filter(s => s.state === ServiceState.CONNECTING).length;
        const disconnectedServices = Array.from(this.services.values()).filter(s => s.state === ServiceState.DISCONNECTED).length;
        const errorServices = Array.from(this.services.values()).filter(s => s.state === ServiceState.ERROR).length;
        const autoConnectServices = Array.from(this.services.values()).filter(s => s.serviceConfig.autoConnect).length;
        const essentialServices = Array.from(this.services.values()).filter(s => s.metadata.essential).length;

        return {
            totalServices,
            connectedServices,
            connectingServices,
            disconnectedServices,
            errorServices,
            autoConnectServices,
            essentialServices
        };
    }

    /**
     * Starts the health check monitoring system.
     */
    private startHealthCheckMonitoring() {
        if (this.healthCheckTimer) {
            return; // Already started
        }

        this.healthCheckTimer = setInterval(async () => {
            try {
                await this.performHealthChecks();
            } catch (error) {
                logger.error("ServiceManager", "Error during scheduled health checks", error instanceof Error ? error : new Error(String(error)));
            }
        }, this.healthCheckInterval);

        logger.debug("ServiceManager", `Health check monitoring started (interval: ${this.healthCheckInterval}ms)`);
    }

    /**
     * Stops the health check monitoring system.
     */
    private stopHealthCheckMonitoring() {
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = undefined;
            logger.debug("ServiceManager", "Health check monitoring stopped");
        }
    }

    /**
     * Initializes the service system by loading default services and starting monitoring.
     * This method should be called during application startup.
     */
    async initialize() {
        logger.info("ServiceManager", "Initializing...");

        // Clear any existing services
        this.services.clear();
        this.connecting.clear();

        // Load test services
        logger.info("ServiceManager", "Loading test services...");
        const testResults = await this.loadServicesFromDir("./test");

        const allResults = [...testResults];
        const successful = allResults.filter(r => r.success).length;
        const failed = allResults.filter(r => !r.success).length;

        logger.info("ServiceManager", `Initialized: ${successful} services registered, ${failed} failed`);

        if (failed > 0) {
            logger.warn("ServiceManager", "Some services failed to register. Check the logs above for details.");
        }

        // Start health check monitoring
        this.startHealthCheckMonitoring();

        // Auto-connect services
        if (successful > 0) {
            setTimeout(async () => {
                await this.connectAutoServices();
            }, 1000); // Give services a moment to initialize
        }
    }

    /**
     * Shuts down the service system by disconnecting all services and stopping monitoring.
     */
    async shutdown() {
        logger.debug("ServiceManager", "Shutting down...");

        // Stop health monitoring
        this.stopHealthCheckMonitoring();

        // Disconnect all services
        const connectedServices = Array.from(this.services.keys()).filter(name => {
            const service = this.services.get(name);
            return service && (service.state === ServiceState.CONNECTED || service.state === ServiceState.CONNECTING);
        });

        for (const serviceName of connectedServices) {
            await this.disconnectService(serviceName);
        }

        // Cleanup all services
        for (const service of this.services.values()) {
            await service.cleanup();
        }

        this.services.clear();
        this.connecting.clear();

        logger.debug("ServiceManager", "Shutdown complete");
    }

    /**
     * Clears all services from the manager.
     * WARNING: This method is intended for testing purposes only.
     */
    public clearAll() {
        this.stopHealthCheckMonitoring();
        this.services.clear();
        this.connecting.clear();
    }
}

export const serviceManager = new ServiceManager();
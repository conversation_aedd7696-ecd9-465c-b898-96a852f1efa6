/**
 * A type alias for a function that listens to an event.
 * It's an async function that receives the event's payload.
 * @template T The type of the event's payload.
 */
export type EventListener<T> = (payload: T) => Promise<void> | void;

/**
 * Represents a specific type of event that can be emitted and listened for.
 * It is uniquely identified by its name. The payload type `T` ensures that
 * listeners receive the correct data structure.
 *
 * @template T The type of the payload that this event carries.
 */
export class Event<T> {
    /**
     * A unique symbol to identify this event type, preventing name collisions.
     * This is used internally by the EventManager.
     */
    public readonly _symbol: symbol;

    /**
     * @param name A human-readable name for the event, used for debugging and logging.
     */
    constructor(public readonly name: string) {
        this._symbol = Symbol(name);
    }
}

<template>
    <div class="page-container scrollable-content">
        <PageHeader title="Connected Services" subtitle="Manage and monitor your connected platforms">
            <template #actions>
                <button class="btn btn-primary">
                    <Icon name="lucide:plus" size="16" />
                    Add Service
                </button>
            </template>
        </PageHeader>

        <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading services...</p>
        </div>

        <div v-else class="content-grid services-grid">
            <!-- Dynamic Service Cards -->
            <div
                v-for="service in services"
                :key="service.name"
                :class="['service-card', 'card', 'glass', service.platform.toLowerCase()]"
            >
                <div class="service-header">
                    <div class="service-info">
                        <div class="service-logo">
                            <Icon :name="getPlatformIcon(service.platform)" size="32" />
                        </div>
                        <div>
                            <h3>{{ service.name }}</h3>
                            <p class="service-status" :class="{ error: service.state === 'ERROR' }">
                                <span
                                    class="status-indicator"
                                    :class="{ error: service.state === 'ERROR' }"
                                    :style="{ backgroundColor: getStatusColor(service.state) }"
                                ></span>
                                {{ service.state }}
                            </p>
                        </div>
                    </div>
                    <button class="service-menu-btn">
                        <Icon name="lucide:more-vertical" size="16" />
                    </button>
                </div>

                <!-- Service Info -->
                <div class="service-info-section">
                    <div class="service-detail">
                        <span class="detail-label">Platform</span>
                        <span class="detail-value">{{ service.platform }}</span>
                    </div>
                    <div class="service-detail">
                        <span class="detail-label">Version</span>
                        <span class="detail-value">{{ service.version }}</span>
                    </div>
                    <div class="service-detail">
                        <span class="detail-label">Description</span>
                        <span class="detail-value">{{ service.description }}</span>
                    </div>
                </div>

                <!-- Connection Stats -->
                <div v-if="service.state === 'CONNECTED'" class="service-stats">
                    <div class="service-stat">
                        <span class="stat-label">Connected</span>
                        <span class="stat-value">{{ formatConnectionTime(service.connectionStats.lastConnected) || 'N/A' }}</span>
                    </div>
                    <div class="service-stat">
                        <span class="stat-label">Attempts</span>
                        <span class="stat-value">{{ service.connectionStats.attempts }}</span>
                    </div>
                    <div class="service-stat">
                        <span class="stat-label">Auto Connect</span>
                        <span class="stat-value">{{ service.autoConnect ? 'Yes' : 'No' }}</span>
                    </div>
                </div>

                <!-- Error Display -->
                <div v-else-if="service.state === 'ERROR'" class="service-error">
                    <p>{{ service.lastError || 'Unknown error occurred' }}</p>
                    <button class="btn btn-error">
                        <Icon name="lucide:refresh-cw" size="16" />
                        Reconnect
                    </button>
                </div>

                <!-- Connecting State -->
                <div v-else-if="service.state === 'CONNECTING' || service.state === 'RECONNECTING'" class="service-connecting">
                    <div class="connecting-spinner"></div>
                    <p>{{ service.state === 'CONNECTING' ? 'Connecting...' : 'Reconnecting...' }}</p>
                </div>

                <!-- Disconnected State -->
                <div v-else class="service-disconnected">
                    <p>Service is disconnected</p>
                    <div class="service-detail">
                        <span class="detail-label">Last Attempt</span>
                        <span class="detail-value">{{ formatConnectionTime(service.connectionStats.lastAttempt) || 'Never' }}</span>
                    </div>
                </div>

                <div class="service-actions">
                    <button class="btn btn-secondary">
                        <Icon name="lucide:settings" size="16" />
                        Configure
                    </button>
                    <button class="btn btn-outline">
                        <Icon name="lucide:file-text" size="16" />
                        View Logs
                    </button>
                </div>
            </div>

            <!-- Add New Service Card -->
            <div class="service-card card glass add-service">
                <div class="add-service-content">
                    <Icon name="lucide:plus-circle" size="48" />
                    <h3>Add New Service</h3>
                    <p>Connect your bot to more platforms</p>
                    <button class="btn btn-primary">Browse Services</button>
                </div>
            </div>

            <!-- No Services State -->
            <div v-if="services.length === 0" class="no-services">
                <Icon name="lucide:wifi-off" size="48" />
                <h3>No Services Found</h3>
                <p>No services are currently registered. Add a service to get started.</p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useServices } from '~/composables/useServices'

const {
  services,
  stats,
  isLoading,
  lastUpdate,
  getStatusIndicator,
  getStatusColor,
  getPlatformIcon,
  formatConnectionTime
} = useServices()
</script>

<style scoped>
/* Loading and empty states */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-xl);
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-secondary);
    border-top: 3px solid var(--color-success);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-services {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-xl);
    text-align: center;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 2px dashed var(--border-color);
}

.no-services svg {
    color: var(--text-secondary);
    margin-bottom: var(--space-md);
}

.no-services h3 {
    font-size: 18px;
    margin-bottom: var(--space-sm);
}

.no-services p {
    color: var(--text-secondary);
}

/* Service card specific styles */

.service-card {
    height: 100%;
    min-height: 460px;
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.service-info {
    display: flex;
    gap: var(--space-md);
    align-items: center;
}

.service-logo {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: var(--bg-secondary);
}

.service-logo svg {
    opacity: 0.9;
}

.discord {
    border-color: var(--discord-color);
}

.discord .service-logo {
    background: rgba(88, 101, 242, 0.2);
    color: var(--discord-color);
}

.twitch {
    border-color: var(--twitch-color);
}

.twitch .service-logo {
    background: rgba(145, 70, 255, 0.2);
    color: var(--twitch-color);
}

.youtube {
    border-color: var(--youtube-color);
}

.youtube .service-logo {
    background: rgba(255, 0, 0, 0.2);
    color: var(--youtube-color);
}

.slack {
    border-color: #864193;
}

.slack .service-logo {
    background: rgba(134, 65, 147, 0.2);
    color: #864193;
}

.service-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.service-status {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 13px;
    color: var(--text-secondary);
}

.service-status.error {
    color: var(--color-error);
}

.service-menu-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: 4px;
    transition: var(--transition);
}

.service-menu-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.service-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.service-stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: var(--space-xs);
}

.stat-value {
    display: block;
    font-size: 20px;
    font-weight: 700;
}

.service-error {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--space-xl);
    background: rgba(244, 67, 54, 0.05);
    border: 1px solid rgba(244, 67, 54, 0.2);
    border-radius: 8px;
    text-align: center;
    gap: var(--space-md);
}

.service-error p {
    color: var(--color-error);
    font-size: 16px;
    font-weight: 500;
}

.service-channels {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.service-channels h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
}

.channel-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    flex: 1;
}

.channel-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-secondary);
    border-radius: 6px;
    font-size: 13px;
}

.channel-name {
    font-weight: 500;
}

.channel-activity {
    color: var(--text-secondary);
    font-size: 12px;
}

.channel-activity.online {
    color: var(--color-success);
    font-weight: 600;
}

.service-actions {
    display: flex;
    gap: var(--space-sm);
    margin-top: auto;
}

.service-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: var(--space-sm);
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.service-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.12);
    color: var(--text-primary);
}

.service-actions .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.service-actions .btn-outline {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    color: var(--text-secondary);
}

.service-actions .btn-outline:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Add Service Card */
.add-service {
    display: flex;
    align-items: center;
    justify-content: center;
    border-style: dashed;
    cursor: pointer;
    transition: var(--transition);
}

.add-service:hover {
    border-color: var(--border-hover);
    background: rgba(255, 255, 255, 0.05);
}

.add-service-content {
    text-align: center;
}

.add-service-content svg {
    color: var(--text-secondary);
    margin-bottom: var(--space-md);
}

.add-service-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.add-service-content p {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
}

/* Service info section */
.service-info-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: 8px;
}

.service-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.detail-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
}

/* Service states */
.service-connecting {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--space-xl);
    text-align: center;
    gap: var(--space-md);
}

.connecting-spinner {
    width: 32px;
    height: 32px;
    border: 2px solid var(--bg-secondary);
    border-top: 2px solid var(--color-warning);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.service-disconnected {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: var(--space-lg);
    text-align: center;
    gap: var(--space-md);
}

.service-disconnected p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Status indicator dot */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--space-xs);
}
</style>
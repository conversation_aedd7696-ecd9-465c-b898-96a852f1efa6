import { ref, computed, onMounted, onUnmounted, watch, readonly } from 'vue'
import { useWebSocket } from './useWebSocket'

export interface ServiceData {
  name: string
  version: string
  description: string
  platform: string
  essential?: boolean
  requiresAuth?: boolean
  state: 'DISCONNECTED' | 'CONNECTING' | 'CONNECTED' | 'RECONNECTING' | 'ERROR'
  autoConnect?: boolean
  connectionTimeout?: number
  maxRetries?: number
  retryDelay?: number
  connectionStats: {
    state: string
    attempts: number
    lastConnected?: string
    lastAttempt?: string
    isRetrying: boolean
  }
  lastError?: string
}

export interface ServiceStats {
  totalServices: number
  connectedServices: number
  connectingServices: number
  disconnectedServices: number
  errorServices: number
  autoConnectServices: number
  essentialServices: number
}

export interface ServicesData {
  services: ServiceData[]
  stats: ServiceStats
}

export function useServices() {
  const { isConnected, addMessageHandler, sendMessage } = useWebSocket()
  
  const services = ref<ServiceData[]>([])
  const stats = ref<ServiceStats>({
    totalServices: 0,
    connectedServices: 0,
    connectingServices: 0,
    disconnectedServices: 0,
    errorServices: 0,
    autoConnectServices: 0,
    essentialServices: 0
  })
  
  const isLoading = ref(true)
  const lastUpdate = ref<Date | null>(null)

  // Computed properties for easy filtering
  const connectedServices = computed(() => 
    services.value.filter(s => s.state === 'CONNECTED')
  )
  
  const disconnectedServices = computed(() => 
    services.value.filter(s => s.state === 'DISCONNECTED')
  )
  
  const errorServices = computed(() => 
    services.value.filter(s => s.state === 'ERROR')
  )

  // Service state helpers
  const getServiceByName = (name: string) => {
    return services.value.find(s => s.name === name)
  }

  const getServiceState = (name: string) => {
    const service = getServiceByName(name)
    return service?.state || 'DISCONNECTED'
  }

  const isServiceConnected = (name: string) => {
    return getServiceState(name) === 'CONNECTED'
  }

  // Load services data
  const loadServices = () => {
    if (isConnected.value) {
      sendMessage({
        type: 'getServices',
        payload: {}
      })
    }
  }

  // Handle incoming service data
  const handleServiceData = (data: ServicesData) => {
    services.value = data.services
    stats.value = data.stats
    lastUpdate.value = new Date()
    isLoading.value = false
  }

  // Handle service updates (real-time)
  const handleServiceUpdate = (update: { event: string; service: ServiceData; error?: string }) => {
    const existingServiceIndex = services.value.findIndex(s => s.name === update.service.name)
    
    if (existingServiceIndex !== -1) {
      // Update existing service
      services.value[existingServiceIndex] = update.service
    } else {
      // Add new service
      services.value.push(update.service)
    }
    
    // Recalculate stats
    stats.value = {
      totalServices: services.value.length,
      connectedServices: services.value.filter(s => s.state === 'CONNECTED').length,
      connectingServices: services.value.filter(s => s.state === 'CONNECTING').length,
      disconnectedServices: services.value.filter(s => s.state === 'DISCONNECTED').length,
      errorServices: services.value.filter(s => s.state === 'ERROR').length,
      autoConnectServices: services.value.filter(s => s.autoConnect).length,
      essentialServices: services.value.filter(s => s.essential).length
    }
    
    lastUpdate.value = new Date()
  }

  // Get status indicator for UI
  const getStatusIndicator = (state: string) => {
    switch (state) {
      case 'CONNECTED': return '🟢'
      case 'CONNECTING': return '🟡'
      case 'RECONNECTING': return '🟠'
      case 'DISCONNECTED': return '⚫'
      case 'ERROR': return '🔴'
      default: return '❓'
    }
  }

  // Get status color for UI
  const getStatusColor = (state: string) => {
    switch (state) {
      case 'CONNECTED': return 'var(--color-success)'
      case 'CONNECTING': return 'var(--color-warning)'
      case 'RECONNECTING': return 'var(--color-warning)'
      case 'DISCONNECTED': return 'var(--text-secondary)'
      case 'ERROR': return 'var(--color-error)'
      default: return 'var(--text-secondary)'
    }
  }

  // Get platform icon name for UI
  const getPlatformIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'discord': return 'simple-icons:discord'
      case 'twitch': return 'simple-icons:twitch'
      case 'youtube': return 'simple-icons:youtube'
      case 'slack': return 'simple-icons:slack'
      case 'test': return 'lucide:test-tube'
      default: return 'lucide:globe'
    }
  }

  // Format connection time
  const formatConnectionTime = (timestamp?: string) => {
    if (!timestamp) return null
    
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)
    
    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    if (minutes > 0) return `${minutes}m ago`
    return 'just now'
  }

  // Setup message handlers
  onMounted(() => {
    // Add message handler for all WebSocket messages
    const removeHandler = addMessageHandler((data: any) => {
      if (data.type === 'serviceData') {
        handleServiceData(data.payload)
      } else if (data.type === 'serviceUpdate') {
        handleServiceUpdate(data.payload)
      }
    })
    
    // Store cleanup function for unmount
    onUnmounted(removeHandler)
    
    // Load initial data when connected
    if (isConnected.value) {
      loadServices()
    }
  })

  // Watch for connection changes and reload data
  const unwatchConnection = watch(isConnected, (connected) => {
    if (connected) {
      loadServices()
    } else {
      isLoading.value = true
    }
  })

  onUnmounted(() => {
    unwatchConnection()
  })

  return {
    // State
    services: readonly(services),
    stats: readonly(stats),
    isLoading: readonly(isLoading),
    lastUpdate: readonly(lastUpdate),
    
    // Computed
    connectedServices,
    disconnectedServices,
    errorServices,
    
    // Methods
    loadServices,
    getServiceByName,
    getServiceState,
    isServiceConnected,
    getStatusIndicator,
    getStatusColor,
    getPlatformIcon,
    formatConnectionTime
  }
} 
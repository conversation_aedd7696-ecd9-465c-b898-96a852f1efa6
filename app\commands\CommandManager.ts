import { CommandStatus, CommandResult, type Command } from "./Command";
import { CoreEvents } from "../events/CoreEvents";
import { eventManager } from "../events/EventManager";
import { logger } from "../../shared";

/**
 * Processes user input strings as commands, executes them, and returns results.
 * It maintains a collection of registered commands and provides methods to add new commands
 * and process input by dynamically loading them from specified directories.
 */
export class CommandManager {
    private commands: Map<string, Command> = new Map();

    /**
     * Adds a new command to the manager.
     * The command name and its aliases (if any) are stored in lowercase to ensure case-insensitive matching.
     * @param command - The command object (implementing Command) to add.
     */
    addCommand(command: Command) {
        // Check for name collisions and warn if found
        if (this.commands.has(command.name.toLowerCase())) {
            logger.warn("CommandManager", `Command name collision: '${command.name}' is already registered. Overwriting...`);
        }
        this.commands.set(command.name.toLowerCase(), command);

        // Check for alias collisions and warn if found
        if (command.aliases) {
            command.aliases.forEach((alias) => {
                const lowerAlias = alias.toLowerCase();
                // Check if the alias is already registered to a different command
                if (this.commands.has(lowerAlias) && this.commands.get(lowerAlias) !== command) {
                    logger.warn("CommandManager", `Command alias collision: '${alias}' for command '${command.name}' conflicts with an existing command or alias`);
                    logger.warn("CommandManager", `Alias '${alias}' will not be registered for '${command.name}'`);
                } else if (!this.commands.has(lowerAlias)) {
                    this.commands.set(lowerAlias, command);
                }
            });
        }
    }

    /**
     * Removes a command and its aliases from the manager.
     * @param commandName - The name of the command to remove.
     * @returns True if the command was found and removed, false otherwise.
     */
    removeCommand(commandName: string): boolean {
        const lowerCommandName = commandName.toLowerCase();
        const command = this.commands.get(lowerCommandName);

        if (!command) {
            logger.debug("CommandManager", `Attempted to remove command '${commandName}' that was not registered`);
            return false;
        }

        // Remove the main command name
        this.commands.delete(lowerCommandName);

        // Remove all aliases that point to this command
        if (command.aliases) {
            command.aliases.forEach((alias) => {
                const lowerAlias = alias.toLowerCase();
                if (this.commands.get(lowerAlias) === command) {
                    this.commands.delete(lowerAlias);
                    logger.debug("CommandManager", `Removed alias '${alias}' for command '${command.name}'`);
                }
            });
        }

        logger.debug("CommandManager", `Removed command '${command.name}' and its aliases`);
        return true;
    }

    /**
     * Gets a command by name or alias.
     * @param commandName - The name or alias of the command to retrieve.
     * @returns The command instance, or undefined if not found.
     */
    getCommand(commandName: string): Command | undefined {
        return this.commands.get(commandName.toLowerCase());
    }

    /**
     * Gets all registered commands.
     * @returns Array of all registered command instances.
     */
    getAllCommands(): Command[] {
        // Get unique commands (filter out aliases)
        const uniqueCommands = new Set<Command>();
        for (const command of this.commands.values()) {
            uniqueCommands.add(command);
        }
        return Array.from(uniqueCommands);
    }

    /**
     * Gets the commands map for plugin integration (read-only access).
     * @returns Read-only reference to the commands map.
     */
    getCommandsMap(): ReadonlyMap<string, Command> {
        return this.commands;
    }

    /**
     * Clears all commands from the manager.
     */
    clearCommands() {
        this.commands.clear();
        logger.debug("CommandManager", "Cleared all commands");
    }

    /**
     * Initializes the CommandManager by loading all commands.
     * This method must be called and awaited before processing any commands.
     *
     * Note: Command loading is now primarily handled by the PluginManager.
     * This method is maintained for backward compatibility and will defer to
     * the PluginManager for command registration.
     */
    async initialize() {
        // Ensure a fresh start if initialize is called multiple times
        this.commands.clear();
        logger.info("CommandManager", "Initialized");
    }

    /**
     * Processes a given input string as a command.
     * It parses the command name and arguments, finds the corresponding command handler,
     * and executes it.
     * @param input - The raw input string from the user.
     * @returns A Promise resolving to a CommandResult object representing the outcome of the command execution.
     */
    async processCommand(input: string): Promise<CommandResult> {
        const trimmedInput = input.trim();
        if (!trimmedInput) {
            return CommandResult.error("Please enter a command. Type 'help' for available commands");
        }

        const [commandNameInput, ...commandArgs] = trimmedInput.split(" ");
        if (!commandNameInput) {
            return CommandResult.error("No command name provided");
        }
        const commandName = commandNameInput.toLowerCase();
        let command = this.commands.get(commandName);

        if (!command) {
            return CommandResult.error(`Unknown command: '${commandNameInput}'. Type 'help' for available commands`);
        }

        // Sub-command logic
        if (command.subCommands && command.subCommands.length > 0 && commandArgs.length > 0) {
            const subCommandName = commandArgs[0]?.toLowerCase();
            if (!subCommandName) {
                return CommandResult.error(`No sub-command name provided for command '${commandNameInput}'. Type 'help' for available commands`);
            }
            const subCommand = command.subCommands.find((sc) => sc.name.toLowerCase() === subCommandName || (sc.aliases && sc.aliases.map(a => a.toLowerCase()).includes(subCommandName)));

            if (subCommand) {
                command = subCommand; // The command to execute is now the sub-command
                commandArgs.shift(); // Remove the sub-command name from the args
            }
            // If no sub-command is found, the original command will be executed, and it can handle the arguments itself (e.g., show help).
        }

        const startTime = performance.now(); // For performance monitoring
        // Initialize result with a default error state to satisfy linter and ensure it's always defined.
        let result: CommandResult = CommandResult.error("Command execution did not properly initialize or complete.");

        try {
            result = await command.execute(commandArgs);
            await eventManager.emit(CoreEvents.commandExecuted, { command, args: commandArgs, result });
            // If command execution results in an error status, ConsoleManager will log it.
        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            await eventManager.emit(CoreEvents.commandErrored, { command, args: commandArgs, error: err });
            result = CommandResult.error(`An error occurred while executing the command '${command.name}'`, err as Error);
        } finally {
            const endTime = performance.now();
            const durationMs = endTime - startTime;

            // 'result' is guaranteed to be initialized, so we can directly access 'result.status'.
            const outcome = result.status === CommandStatus.ERROR ? "errored" : "completed";
            logger.debug("CommandManager", `Command '${input}' ${outcome} in ${durationMs.toFixed(3)} ms`);
        }
        return result;
    }
}

export const commandManager = new CommandManager();

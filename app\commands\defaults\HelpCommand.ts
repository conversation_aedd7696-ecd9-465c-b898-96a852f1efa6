import { CommandResult, type Command } from "../Command";
import { commandManager } from "../../commands/CommandManager";
import { logger } from "../../../shared";
import { Globals } from "../../../shared";

/**
 * Command that displays help for available commands.
 *
 * The help command can be used in two ways:
 * - `help`: Displays a list of all available commands.
 * - `help <command>`: Shows detailed information for a specific command.
 *
 * @implements {Command}
 */
export default class HelpCommand implements Command {
    readonly name = "help";
    readonly aliases = ["?"];
    readonly description = "Show available commands or help for a specific command";

    /**
     * Executes the help command.
     *
     * @param args - Command arguments
     * @returns A CommandResult object indicating successful execution
     */
    execute(args: string[]): CommandResult {
        // Regular expression pattern to match command names
        const COMMAND_NAME_PATTERN = /^[a-zA-Z0-9-]+$/;

        // If a command name is provided, show detailed help for that command
        if (args.length > 0 && args[0]) {
            const commandName = args[0].toLowerCase();

            // Validate command name format
            if (!COMMAND_NAME_PATTERN.test(commandName)) {
                return CommandResult.error(`Invalid command name format: '${commandName}'`);
            }

            // Find command by name or alias (case-insensitive)
            // We need to get this from the CommandManager's commands map
            const commandsMap: ReadonlyMap<string, Command> = commandManager.getCommandsMap();
            const command: Command | undefined = Array.from(commandsMap.values()).find(
                (cmd: Command) =>
                    cmd.name.toLowerCase() === commandName ||
                    (cmd.aliases && cmd.aliases.some((alias: string) => alias.toLowerCase() === commandName))
            );

            // Display detailed help for the requested command
            if (command) {
                logger.section(`Help: ${command.name}`);
                logger.blank(command.description);
                if (command.usage) {
                    logger.keyValue("Usage", `plugin ${command.usage}`, 15);
                }
                if (command.aliases && command.aliases.length > 0) {
                    logger.keyValue("Aliases", command.aliases.join(", "), 15);
                }

                if (command.subCommands && command.subCommands.length > 0) {
                    logger.newLine();
                    logger.blank("Sub-commands:");
                    command.subCommands.forEach((sub) => {
                        let subUsage = sub.usage ? sub.usage : sub.name;
                        if (sub.aliases && sub.aliases.length > 0) {
                            subUsage += ` (${sub.aliases.join(", ")})`;
                        }
                        logger.keyValue(`  ${sub.name}`, `${sub.description} (Usage: ${command.name} ${subUsage})`, 15);
                    });
                }
                logger.newLine();
                return CommandResult.okay();
            } else {
                return CommandResult.error(`Command '${commandName}' not found`);
            }
        } else {
            // No specific command requested - show general help
            this.showGeneralHelp();
            return CommandResult.okay();
        }
    }

    /**
     * Displays general help with all available commands.
     */
    private showGeneralHelp() {
        logger.section(`${Globals.NAME} v${Globals.VERSION} - Available Commands`);

        // Get all commands from CommandManager
        const allCommands: Command[] = commandManager.getAllCommands();

        // Sort commands alphabetically, excluding help command to avoid self-reference
        const sortedCommands = allCommands
            .filter((cmd: Command) => cmd.name !== "help")
            .sort((a: Command, b: Command) => a.name.localeCompare(b.name));

        // Display each command with its description
        sortedCommands.forEach((command: Command) => {
            let displayName = command.name;
            if (command.aliases && command.aliases.length > 0) {
                displayName += ` (${command.aliases.join(", ")})`;
            }
            logger.keyValue(displayName, command.description, 15);
        });

        logger.newLine();
        logger.blank(`Type 'help <command>' for more information on a specific command.`);
        logger.newLine();
    }
}

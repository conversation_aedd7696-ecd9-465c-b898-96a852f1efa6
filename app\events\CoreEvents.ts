import type { Command, CommandResult } from "../commands/Command";
import type { Plugin } from "../plugins/Plugin";
import type { Service, ServiceResult } from "../services/Service";
import { Event } from "./Event";

/**
 * Defines the payload for the `command:executed` event.
 */
export interface CommandExecutedPayload {
    /** The command that was executed. */
    command: Command;
    /** The arguments passed to the command. */
    args: string[];
    /** The result of the command execution. */
    result: CommandResult;
}

/**
 * Defines the payload for the `command:errored` event.
 */
export interface CommandErroredPayload {
    /** The command that failed. */
    command: Command;
    /** The arguments passed to the command. */
    args: string[];
    /** The error that occurred. */
    error: Error;
}

/**
 * Defines the payload for the `plugin:error` event.
 */
export interface PluginErrorPayload {
    /** The plugin that encountered an error. */
    plugin: Plugin;
    /** The error that occurred. */
    error: Error;
}

/**
 * Defines the payload for the `service:error` event.
 */
export interface ServiceErrorPayload {
    /** The service that encountered an error. */
    service: Service;
    /** The error that occurred. */
    error: Error;
}

/**
 * Defines the payload for the `service:messageSent` event.
 */
export interface ServiceMessageSentPayload {
    /** The service that sent the message. */
    service: Service;
    /** The message that was sent. */
    message: any;
    /** The result of the message send operation. */
    result: ServiceResult;
}

/**
 * Defines the core events used throughout the application.
 * Using this central file helps avoid circular dependencies and provides
 * a single source of truth for all major application events.
 */
export const CoreEvents = {
    /**
     * Fired when a plugin has been successfully loaded.
     * The payload is the instance of the loaded plugin.
     */
    pluginLoaded: new Event<Plugin>("plugin:loaded"),

    /**
     * Fired when a plugin has been successfully unloaded.
     * The payload is the instance of the unloaded plugin.
     */
    pluginUnloaded: new Event<Plugin>("plugin:unloaded"),

    /**
     * Fired when a plugin has been successfully reloaded.
     * The payload is the instance of the reloaded plugin.
     */
    pluginReloaded: new Event<Plugin>("plugin:reloaded"),

    /**
     * Fired when a plugin is about to be loaded.
     * The payload is the instance of the plugin being loaded.
     */
    pluginLoading: new Event<Plugin>("plugin:loading"),

    /**
     * Fired when a plugin is about to be unloaded.
     * The payload is the instance of the plugin being unloaded.
     */
    pluginUnloading: new Event<Plugin>("plugin:unloading"),

    /**
     * Fired when a plugin encounters an error during its lifecycle.
     * The payload contains the plugin and the error.
     */
    pluginError: new Event<PluginErrorPayload>("plugin:error"),

    /**
     * Fired when a service is attempting to connect.
     * The payload is the instance of the service connecting.
     */
    serviceConnecting: new Event<Service>("service:connecting"),

    /**
     * Fired when a service has successfully connected.
     * The payload is the instance of the connected service.
     */
    serviceConnected: new Event<Service>("service:connected"),

    /**
     * Fired when a service is attempting to disconnect.
     * The payload is the instance of the service disconnecting.
     */
    serviceDisconnecting: new Event<Service>("service:disconnecting"),

    /**
     * Fired when a service has successfully disconnected.
     * The payload is the instance of the disconnected service.
     */
    serviceDisconnected: new Event<Service>("service:disconnected"),

    /**
     * Fired when a service is attempting to reconnect.
     * The payload is the instance of the service reconnecting.
     */
    serviceReconnecting: new Event<Service>("service:reconnecting"),

    /**
     * Fired when a service encounters an error during its lifecycle.
     * The payload contains the service and the error.
     */
    serviceError: new Event<ServiceErrorPayload>("service:error"),

    /**
     * Fired when a service successfully sends a message.
     * The payload contains the service, message, and result.
     */
    serviceMessageSent: new Event<ServiceMessageSentPayload>("service:messageSent"),

    /**
     * Fired when a command is successfully executed.
     * The payload contains the command, its arguments, and the result.
     */
    commandExecuted: new Event<CommandExecutedPayload>("command:executed"),

    /**
     * Fired when a command execution fails with an error.
     * The payload contains the command, its arguments, and the error.
     */
    commandErrored: new Event<CommandErroredPayload>("command:errored"),

    /**
     * Fired when a line of input is received from the console.
     * The payload is the raw string that was entered.
     */
    consoleLineReceived: new Event<string>("console:lineReceived"),

    /**
     * Fired just before the application starts its initialization process.
     * The payload is void.
     */
    appStarting: new Event<void>("app:starting"),

    /**
     * Fired after the application has successfully initialized all modules.
     * The payload is void.
     */
    appReady: new Event<void>("app:ready"),

    /**
     * Fired when a command is processed.
     * The payload contains the input and the result of the command execution.
     */
    commandProcessed: new Event<{ input: string; result: CommandResult }>("commandProcessed"),

    /**
     * Fired when the application is shutting down.
     * The payload is void.
     */
    appShutdown: new Event<void>("appShutdown"),

    /** Emitted when a log message is created. Payload: The log object. */
    log: new Event<{ level: string; message: string; parts: any[]; context?: string }>("core:log"),
};

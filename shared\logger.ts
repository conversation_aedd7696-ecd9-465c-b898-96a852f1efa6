import { Globals } from "./Globals";
//import { format } from "node:util";
//import { eventManager } from "../app/events/EventManager";
//import { CoreEvents } from "../app/events/CoreEvents";

/**
 * Defines the available logging levels.
 */
export enum LogLevel {
    /** Informational messages. */
    INFO,
    /** Error messages. */
    ERROR,
    /** Debug messages, typically for development. */
    DEBUG,
    /** Warning messages. */
    WARN,
}

/**
 * ANSI color codes for styling console output.
 * Provides a set of predefined colors to enhance readability of logs.
 */
const colors = {
    reset: "\x1b[0m",
    bright: "\x1b[1m",
    dim: "\x1b[2m",
    red: "\x1b[31m",
    green: "\x1b[32m",
    yellow: "\x1b[33m",
    blue: "\x1b[34m",
    magenta: "\x1b[35m",
    cyan: "\x1b[36m",
    gray: "\x1b[90m",
} as const;

/**
 * Provides a flexible logging utility with support for different log levels,
 * timestamps, colored output, and various formatting options.
 */
export class Logger {
    /**
     * Get current timestamp in HH:MM:SS.mmm format with high-precision timing
     */
    private getTimestamp(): string {
        const now = new Date();
        const hours = now.getHours().toString().padStart(2, "0");
        const minutes = now.getMinutes().toString().padStart(2, "0");
        const seconds = now.getSeconds().toString().padStart(2, "0");
        const millisPart = Math.floor(performance.now() % 1000).toString().padStart(3, "0");
        return `${hours}:${minutes}:${seconds}.${millisPart}`;
    }

    /**
     * Format message with timestamp, level prefix, and optional color
     */
    // todo: service?
    private formatMessage(service: string, message: string, level?: LogLevel, skipTimestamp?: boolean): string {
        if (skipTimestamp) {
            return message;
        }
        const timestamp = this.getTimestamp();
        const coloredTimestamp = `${colors.gray}${timestamp}${colors.reset}`;
        const coloredService = `${colors.cyan}${service.padStart(14)}${colors.reset}`;

        // Determine level prefix with padding for alignment
        let levelPrefix = "";
        switch (level) {
            case LogLevel.ERROR:
                levelPrefix = `${colors.red}ERROR${colors.reset}`;
                break;
            case LogLevel.DEBUG:
                levelPrefix = `${colors.magenta}DEBUG${colors.reset}`;
                break;
            case LogLevel.WARN:
                levelPrefix = `${colors.yellow} WARN${colors.reset}`;
                break;
            default:
                levelPrefix = `${colors.green} INFO${colors.reset}`;
                break;
        }
        return `${coloredTimestamp} ${levelPrefix} ${coloredService} ${message}${colors.reset}`;
    }

    /**
     * Log a message with newline (replaces console.log)
     * @param service - The service that is logging the message
     * @param message - The message to log
     * @param context - Where the log should appear (default: BOTH)
     * @param args - Optional additional arguments to log
     */
    info(service: string, message: string, ...args: any[]) {
        const formattedMessage = args.length > 0 ? `${message} ${args.join(" ")}` : message;
        console.log(this.formatMessage(service, formattedMessage, LogLevel.INFO));

        // Only emit event if context allows web logging
        /** TODO: fix
        eventManager.emit(CoreEvents.log, {
            level: "INFO",
            service: service,
            message: formattedMessage,
            parts: args,
        });
        */
    }

    /**
     * Log an error message (replaces console.error)
     * @param service - The service that is logging the message
     * @param message - The error message to log
     * @param contextOrError - Where the log should appear, or the error object
     * @param args - Optional additional arguments to log
     */
    error(service: string, message: string, error?: Error, ...args: any[]) {
        const allArgs: any[] = [];

        allArgs.push(...args);
        if (error) {
            allArgs.push(error);
        }

        let fullMessage = message;
        if (allArgs.length > 0) {
            fullMessage = `${message}\n${allArgs.map((arg) => (arg instanceof Error ? arg.stack || arg.message : arg)).join(" ")}`;
        }

        console.error(this.formatMessage(service, fullMessage, LogLevel.ERROR));

        /** TODO: fix
        eventManager.emit(CoreEvents.log, {
            level: "ERROR",
            service: service,
            message: fullMessage,
            parts: allArgs,
        });
        */
    }

    /**
     * Warning messages
     * @param service - The service that is logging the message
     * @param message - The message to log
     * @param context - Where the log should appear (default: BOTH)
     * @param args - Optional additional arguments to log
     */
    warn(service: string, message: string, ...args: any[]) {
        const formattedMessage = args.length > 0 ? `${message} ${args.join(" ")}` : message;
        console.warn(this.formatMessage(service, formattedMessage, LogLevel.WARN));

        // Only emit event if context allows web logging
        /** TODO: fix
        eventManager.emit(CoreEvents.log, {
            level: "WARN",
            service: service,
            message: formattedMessage,
            parts: args,
        });
        */
    }

    /**
     * Debug logging (can be toggled on/off)
     * @param service - The service that is logging the message
     * @param message - The message to log
     * @param context - Where the log should appear (default: BOTH)
     * @param args - Optional additional arguments to log
     */
    debug(service: string, message: string, ...args: any[]) {
        // Only log in development or when DEBUG=true
        if (Globals.DEBUG_ENABLED) {
            const formattedMessage = args.length > 0 ? `${message} ${args.join(" ")}` : message;
            console.log(this.formatMessage(service, formattedMessage, LogLevel.DEBUG));

            // Only emit event if context allows web logging
            /** TODO: fix
            if (context !== LogContext.CONSOLE) {
                eventManager.emit(CoreEvents.log, {
                    level: "DEBUG",
                    message: formattedMessage,
                    parts: args,
                    context: context,
                });
            }
            */
        }
    }

    /**
     * Log a line with no formatting
     * @param text - The text to log
     * @param context - Where the log should appear (default: BOTH)
     */
    // todo: service?
    blank(text: string) {
        console.log(text);

        // Only emit event if context allows web logging
        /** TODO: fix
        eventManager.emit(CoreEvents.log, { level: "INFO", service: service, message: text, parts: [], context: context });
        */
    }

    /**
     * Log an empty line for spacing
     * @param context - Where the log should appear (default: BOTH)
     */
    newLine() {
        console.log();

        // Only emit event if context allows web logging
        /** TODO: fix
        eventManager.emit(CoreEvents.log, { level: "INFO", service: service, message: "", parts: [], context: context });
        */
    }

    /**
     * Write inline text without newline (replaces process.stdout.write)
     * @param text - The text to write
     */
    prompt() {
        const timestamp = this.getTimestamp();
        const coloredPrompt = `${colors.gray}${timestamp}${colors.reset} ${colors.green}${Globals.NAME} > ${colors.reset}`;
        process.stdout.write(coloredPrompt);
    }

    /**
     * Log a section header with spacing
     * @param title - The section title to log
     * @param context - Where the log should appear (default: BOTH)
     */
    // todo: service?
    section(title: string) {
        this.newLine();
        const message = this.formatMessage("", `${colors.bright}${title}${colors.reset}`, undefined, true);
        console.log(message);

        /** TODO: fix
        eventManager.emit(CoreEvents.log, { level: "INFO", message: title, parts: [], context: context });
        */
    }

    /**
     * Log multiple lines with consistent formatting
     * @param service - The service that is logging the message
     * @param lines - Array of strings to log
     */
    block(service: string, lines: string[]) {
        lines.forEach((line) => this.info(service, line));
    }

    /**
     * Log with custom formatting for status/help output
     * @param key - The key to display
     * @param value - The value to display
     * @param padding - Optional padding for the key (default is 15)
     * @param context - Where the log should appear (default: BOTH)
     */
    // todo: service?
    keyValue(key: string, value: string, padding: number = 15) {
        const formattedKey = `${colors.cyan}${key.padEnd(padding)}${colors.reset}`;
        const formattedValue = `${colors.dim}${value}${colors.reset}`;
        const message = this.formatMessage("", `  ${formattedKey} - ${formattedValue}`, undefined, true);

        console.log(message);

        /** TODO: fix
        const plainMessage = `  ${key.padEnd(padding)} - ${value}`;
        eventManager.emit(CoreEvents.log, { level: "INFO", message: plainMessage, parts: [], context: context });
        */
    }
}

/**
 * A default, shared instance of the Logger
 * This instance can be imported and used throughout the application for consistent logging.
 */
export const logger = new Logger();
